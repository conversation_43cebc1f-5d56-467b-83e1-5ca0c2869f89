/**
 * Invoice validator middleware
 */
const { body, validationResult } = require('express-validator');
const AppError = require('../utils/error');

/**
 * Middleware to handle validation errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

/**
 * Validate create invoice request
 */
exports.validateCreateInvoice = [
  // Basic invoice validation
  body('invoice_date')
    .isISO8601()
    .withMessage('Valid invoice date is required in ISO format (YYYY-MM-DD)'),

  body('due_date')
    .isISO8601()
    .withMessage('Valid due date is required in ISO format (YYYY-MM-DD)'),

  body('customer_id')
    .optional({ nullable: false, checkFalsy: true })
    .isInt()
    .withMessage('Customer ID must be an integer'),

  body('supplier_id')
    .optional({ nullable: false, checkFalsy: true })
    .isInt()
    .withMessage('Supplier ID must be an integer'),

  body('type')
    .notEmpty()
    .isIn(['supplier', 'customer'])
    .withMessage('Type must be either "supplier" or "customer"'),

  // Conditional validation for customer_id and supplier_id based on type
  body('customer_id')
    .if(body('type').equals('customer'))
    .notEmpty()
    .withMessage('Customer ID is required for customer invoices'),

  body('supplier_id')
    .if(body('type').equals('supplier'))
    .notEmpty()
    .withMessage('Supplier ID is required for supplier invoices'),

  body('lpo_number')
    .optional()
    .isString()
    .withMessage('LPO number must be a string'),

  body('terms')
    .optional()
    .isString()
    .withMessage('Terms must be a string'),

  body('kra_integrated')
    .optional()
    .isBoolean()
    .withMessage('KRA integrated must be a boolean'),

  // Items validation
  body('items')
    .isArray({ min: 1 })
    .withMessage('At least one item is required'),

  body('items.*.description')
    .isString()
    .notEmpty()
    .withMessage('Item description is required'),

  body('items.*.quantity')
    .isFloat({ min: 0.01 })
    .withMessage('Quantity must be greater than 0'),

  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a valid number'),

  body('items.*.total_price')
    .isFloat({ min: 0 })
    .withMessage('Total price must be a valid number'),

  body('items.*.product_id')
    .optional()
    .isInt()
    .withMessage('Product ID must be an integer'),

  body('items.*.vat_rate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('VAT rate must be a valid number'),

  body('items.*.vat_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('VAT amount must be a valid number'),

  // Totals validation
  body('subtotal')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Subtotal must be a valid number'),

  body('vat_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('VAT amount must be a valid number'),

  body('total_amount')
    .isFloat({ min: 0 })
    .withMessage('Total amount must be a valid number'),

  // Notes
  body('notes')
    .optional()
    .isString()
    .withMessage('Notes must be a string'),

  // Handle validation errors
  handleValidationErrors
];
