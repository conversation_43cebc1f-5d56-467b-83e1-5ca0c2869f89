const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

const Invoice = sequelize.define('invoice', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  invoice_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  dno: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Delivery note number reference'
  },
  invoice_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  lpo_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  terms: {
    type: DataTypes.STRING(100),
    allowNull: true
  },

  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  supplier_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'suppliers',
      key: 'id'
    }
  },
  tenant_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('supplier', 'customer'),
    allowNull: false,
    defaultValue: 'customer',
    comment: 'Type of invoice - supplier for commission invoices, customer for DSA invoices'
  },
  subtotal: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  vat_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0
  },
  total_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  total_paid: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
    comment: 'Total amount paid through invoice payments'
  },
  status: {
    type: DataTypes.ENUM('draft', 'sent', 'paid', 'partially_paid', 'overdue', 'cancelled', 'reconciled'),
    defaultValue: 'draft'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  // KRA integration fields
  kra_integrated: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this invoice should be integrated with KRA'
  },
  kra_verification_code: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  kra_fiscal_receipt_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  kra_verification_url: { 
    type: DataTypes.STRING(255),
    allowNull: true
  },
  kra_verification_timestamp: {
    type: DataTypes.DATE,
    allowNull: true
  },
  kra_integration_status: {
    type: DataTypes.ENUM('pending', 'completed', 'failed', 'offline'),
    defaultValue: 'pending'
  },
  kra_response_data: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  cu_serial_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  cu_invoice_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  }
}, {
  tableName: 'invoices',
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = Invoice;

