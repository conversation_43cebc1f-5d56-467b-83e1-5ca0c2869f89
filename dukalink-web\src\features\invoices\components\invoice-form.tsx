"use client";

import { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useRouter } from "next/navigation";
import { CreateInvoiceRequest, Invoice, UpdateInvoiceRequest } from "../types";
import { useCustomers } from "@/features/customers/hooks/use-customers";
import { useSuppliers } from "@/features/inventory/hooks/use-suppliers";
import { Trash2, Plus } from "lucide-react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import { useVatRates } from "../hooks/use-vat-rates";

// Define the form schema
const invoiceFormSchema = z
  .object({
    type: z.enum(["customer", "supplier"]),
    customer_id: z.number().optional().nullable(),
    supplier_id: z.number().optional().nullable(),
    invoice_date: z.date(),
    due_date: z.date(),
    lpo_number: z.string().optional(),
    terms: z.string().optional(),
    notes: z.string().optional(),
    kra_integrated: z.boolean().default(false),
    items: z.array(
      z.object({
        description: z.string().min(1, "Description is required"),
        quantity: z.number().min(0.01, "Quantity must be greater than 0"),
        unit_price: z.number().min(0, "Unit price must be a valid number"),
        total_price: z.number().min(0, "Total price must be a valid number"),
        vat_rate: z.number().optional().nullable(),
        vat_amount: z.number().optional().nullable(),
        is_vat_exempt: z.boolean().default(false),
      })
    ).min(1, "At least one item is required"),
    subtotal: z.number().min(0),
    vat_amount: z.number().min(0),
    total_amount: z.number().min(0),
  })
  .refine(
    (data) => {
      // If type is customer, customer_id must be provided
      if (data.type === "customer") {
        return !!data.customer_id;
      }
      // If type is supplier, supplier_id must be provided
      if (data.type === "supplier") {
        return !!data.supplier_id;
      }
      return true;
    },
    {
      message: "Please select a customer or supplier based on invoice type",
      path: ["customer_id", "supplier_id"], // This will show the error on both fields
    }
  );

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;

interface InvoiceFormProps {
  invoice?: Invoice;
  onSubmit: (data: CreateInvoiceRequest | UpdateInvoiceRequest) => void;
  isSubmitting: boolean;
}

export function InvoiceForm({
  invoice,
  onSubmit,
  isSubmitting,
}: InvoiceFormProps) {
  const router = useRouter();
  const { data: customersData } = useCustomers();
  const { data: suppliersData } = useSuppliers();
  const customers = customersData?.data || [];
  const suppliers = suppliersData?.data || [];

  // Initialize form with default values or existing invoice data
  const form = useForm<InvoiceFormValues>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: invoice
      ? {
          type: invoice.type,
          customer_id: invoice.customer_id || null,
          supplier_id: invoice.supplier_id || null,
          invoice_date: new Date(invoice.invoice_date),
          due_date: new Date(invoice.due_date),
          lpo_number: invoice.lpo_number || "",
          terms: invoice.terms || "",
          notes: invoice.notes || "",
          kra_integrated: invoice.kra_integrated || false,
          items: invoice.items?.map((item) => ({
            description: item.description || "",
            quantity: Number(item.quantity) || 0,
            unit_price: Number(item.unit_price) || 0,
            total_price: Number(item.total_price) || 0,
            vat_rate: item.vat_rate !== undefined ? Number(item.vat_rate) || 0 : 0,
            vat_amount: item.vat_amount !== undefined ? Number(item.vat_amount) || 0 : null,
            is_vat_exempt: item.is_vat_exempt === true,
          })) || [],
          subtotal: Number(invoice.subtotal) || 0,
          vat_amount: Number(invoice.vat_amount) || 0,
          total_amount: Number(invoice.total_amount) || 0,
        }
      : {
          type: "customer",
          customer_id: null,
          supplier_id: null,
          invoice_date: new Date(),
          due_date: new Date(),
          lpo_number: "",
          terms: "",
          notes: "",
          kra_integrated: false,
          items: [
            {
              description: "",
              quantity: 1,
              unit_price: 0,
              total_price: 0,
              vat_rate: null,
              vat_amount: null,
              is_vat_exempt: false,
            },
          ],
          subtotal: 0,
          vat_amount: 0,
          total_amount: 0,
        },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch form values for calculations
  const watchItems = form.watch("items");
  const watchType = form.watch("type");

  // Calculate totals when items change
  useEffect(() => {
    if (watchItems) {
      // Calculate total amount (sum of all item total prices)
      const totalAmount = watchItems.reduce(
        (sum, item) => sum + (item.total_price || 0),
        0
      );

      // Calculate total VAT amount (sum of all item VAT amounts)
      const vatAmount = watchItems.reduce(
        (sum, item) => sum + (item.vat_amount || 0),
        0
      );

      // Calculate vatable amount (total minus VAT)
      const vatableAmount = totalAmount - vatAmount;

      // Update form values
      form.setValue("subtotal", vatableAmount); // Renamed to "vatable amount" in UI
      form.setValue("vat_amount", vatAmount);
      form.setValue("total_amount", totalAmount);
    }
  }, [watchItems, form]);

  // Handle form submission
  const handleSubmit = (values: InvoiceFormValues) => {
    // Create a base object with common fields
    const baseData = {
      type: values.type,
      invoice_date: format(values.invoice_date, "yyyy-MM-dd"),
      due_date: format(values.due_date, "yyyy-MM-dd"),
      lpo_number: values.lpo_number,
      terms: values.terms,
      notes: values.notes,
      kra_integrated: values.kra_integrated, // Include KRA integration flag
      subtotal: values.subtotal,
      vat_amount: values.vat_amount,
      total_amount: values.total_amount,
      items: values.items.map((item) => ({
        ...item,
        vat_rate: item.vat_rate || undefined,
        vat_amount: item.vat_amount || undefined,
      })),
    };

    // Add the appropriate ID field based on invoice type
    const formattedData = {
      ...baseData,
      ...(values.type === "customer" && values.customer_id ? { customer_id: values.customer_id } : {}),
      ...(values.type === "supplier" && values.supplier_id ? { supplier_id: values.supplier_id } : {}),
    };

    console.log("Submitting invoice data:", formattedData);
    onSubmit(formattedData);
  };

  // Fetch VAT rates
  const { data: vatRates = [] } = useVatRates();

  // Calculate item total when quantity or unit price changes
  const calculateItemTotal = (index: number) => {
    const item = form.getValues(`items.${index}`);

    // Ensure values are numbers
    const quantity = typeof item.quantity === 'string'
      ? parseFloat(item.quantity) || 0
      : item.quantity || 0;

    const unitPrice = typeof item.unit_price === 'string'
      ? parseFloat(item.unit_price) || 0
      : item.unit_price || 0;

    // Calculate total price from quantity and unit price
    const totalPrice = quantity * unitPrice;

    // Calculate VAT only if KRA integrated
    let vatAmount = 0;
    let isVatExempt = false;
    const kraIntegrated = form.getValues("kra_integrated");

    if (kraIntegrated) {
      // Ensure VAT rate is always a number (0 if not provided)
      const vatRate = typeof item.vat_rate === 'string'
        ? parseFloat(item.vat_rate) || 0
        : typeof item.vat_rate === 'number'
        ? item.vat_rate
        : 0;

      // If VAT rate is 0, mark as exempt
      isVatExempt = vatRate === 0;

      if (!isVatExempt) {
        vatAmount = totalPrice * (vatRate / 100);
      }

      // Always set VAT rate to a number (0 for exempt, actual rate for taxable)
      form.setValue(`items.${index}.vat_rate`, vatRate);
    } else {
      // For non-KRA invoices, always exempt with 0 VAT rate
      isVatExempt = true;
      form.setValue(`items.${index}.vat_rate`, 0);
    }

    // Set the calculated values with parsed numbers
    form.setValue(`items.${index}.total_price`, parseFloat(totalPrice.toFixed(2)));
    form.setValue(`items.${index}.vat_amount`, parseFloat(vatAmount.toFixed(2)));
    form.setValue(`items.${index}.is_vat_exempt`, isVatExempt);
  };



  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Invoice Type */}
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1">
                  Invoice Type
                  <span className="text-red-500">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select invoice type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="customer">Customer Invoice</SelectItem>
                    <SelectItem value="supplier">Supplier Invoice</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Select whether this is a customer or supplier invoice
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Customer or Supplier */}
          {watchType === "customer" ? (
            <FormField
              control={form.control}
              name="customer_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Customer
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value?.toString() || ""}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select customer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem
                          key={customer.id}
                          value={customer.id.toString()}
                        >
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Customer is required for customer invoices
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            <FormField
              control={form.control}
              name="supplier_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Supplier
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value?.toString() || ""}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select supplier" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {suppliers.map((supplier) => (
                        <SelectItem
                          key={supplier.id}
                          value={supplier.id.toString()}
                        >
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Supplier is required for supplier invoices
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Invoice Date */}
          <FormField
            control={form.control}
            name="invoice_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Invoice Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Due Date */}
          <FormField
            control={form.control}
            name="due_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Due Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* LPO Number */}
          <FormField
            control={form.control}
            name="lpo_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>LPO Number</FormLabel>
                <FormControl>
                  <Input placeholder="LPO-12345" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Terms */}
          <FormField
            control={form.control}
            name="terms"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Terms</FormLabel>
                <FormControl>
                  <Input placeholder="Net 30" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* KRA Integration Toggle */}
          <FormField
            control={form.control}
            name="kra_integrated"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    KRA Integration
                  </FormLabel>
                  <FormDescription>
                    Enable KRA integration for this invoice (includes VAT calculations)
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* Invoice Items */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Invoice Items</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() =>
                append({
                  description: "",
                  quantity: 1,
                  unit_price: 0,
                  total_price: 0,
                  vat_rate: null,
                  vat_amount: null,
                  is_vat_exempt: false,
                })
              }
            >
              <Plus className="mr-2 h-4 w-4" /> Add Item
            </Button>
          </div>

          {fields.map((field, index) => (
            <Card key={field.id}>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {/* Description */}
                  <FormField
                    control={form.control}
                    name={`items.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Input placeholder="Item description" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  {/* Quantity */}
                  <FormField
                    control={form.control}
                    name={`items.${index}.quantity`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Quantity</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            {...field}
                            onChange={(e) => {
                              field.onChange(parseFloat(e.target.value) || 0);
                              calculateItemTotal(index);
                            }}
                            onWheel={(e) => e.currentTarget.blur()}
                            onKeyDown={(e) => {
                              if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                                e.preventDefault();
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Unit Price */}
                  <FormField
                    control={form.control}
                    name={`items.${index}.unit_price`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unit Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            {...field}
                            onChange={(e) => {
                              field.onChange(parseFloat(e.target.value) || 0);
                              calculateItemTotal(index);
                            }}
                            onWheel={(e) => e.currentTarget.blur()}
                            onKeyDown={(e) => {
                              if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                                e.preventDefault();
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* VAT Rate - Only show if KRA integrated */}
                  {form.watch("kra_integrated") && (
                    <FormField
                      control={form.control}
                      name={`items.${index}.vat_rate`}
                      render={({ field }) => {
                        const isVatExempt = form.getValues(`items.${index}.is_vat_exempt`);

                        return (
                          <FormItem>
                            <FormLabel>VAT Rate (%)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                {...field}
                                onChange={(e) => {
                                  const value = e.target.value !== "" ? parseFloat(e.target.value) : 0;
                                  field.onChange(value);
                                  calculateItemTotal(index);
                                }}
                                value={field.value !== null && field.value !== undefined ? field.value : ""}
                                placeholder="Enter VAT rate (0 for exempt)"
                                onWheel={(e) => e.currentTarget.blur()}
                                onKeyDown={(e) => {
                                  if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                                    e.preventDefault();
                                  }
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              {isVatExempt ? "VAT exempt (0%)" : "VAT rate percentage (0 for exempt)"}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        );
                      }}
                    />
                  )}


                  {/* Total Price (calculated) */}
                  <FormField
                    control={form.control}
                    name={`items.${index}.total_price`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Total Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            {...field}
                            readOnly
                            className="bg-gray-50"
                          />
                        </FormControl>
                        <FormDescription>
                          Calculated: Quantity × Unit Price
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Remove Item Button */}
                {fields.length > 1 && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => remove(index)}
                    className="mt-2"
                  >
                    <Trash2 className="mr-2 h-4 w-4" /> Remove Item
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Notes */}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Additional notes or information"
                  {...field}
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Totals */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="subtotal"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Vatable Amount</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    {...field}
                    readOnly
                  />
                </FormControl>
                <FormDescription>
                  Total amount minus VAT
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="vat_amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>VAT Amount</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    {...field}
                    readOnly
                    className="bg-gray-50"
                  />
                </FormControl>
                <FormDescription>
                  Calculated based on VAT rates
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="total_amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Total Amount</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    {...field}
                    readOnly
                    className="font-bold bg-gray-50"
                  />
                </FormControl>
                <FormDescription>
                  Sum of all item totals
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : invoice ? "Update Invoice" : "Create Invoice"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
