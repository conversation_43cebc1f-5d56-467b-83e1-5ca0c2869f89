import { Customer } from "@/features/customers/types";
import { Supplier } from "@/features/inventory/types/supplier";
import { PaginatedResponse } from "@/types/api";

export type InvoiceStatus =
  | 'draft'
  | 'sent'
  | 'paid'
  | 'partially_paid'
  | 'overdue'
  | 'cancelled'
  | 'reconciled';

export type InvoiceType = 'supplier' | 'customer';

export interface InvoiceItem {
  id: number;
  invoice_id: number;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  product_id?: number;
  vat_rate?: number;
  vat_amount?: number;
  is_vat_exempt?: boolean;
  created_at: string;
  updated_at: string;
}

export interface InvoicePayment {
  id: number;
  invoice_id: number;
  payment_date: string;
  amount: number;
  payment_method: string;
  reference_number?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Invoice {
  id: number;
  invoice_number: string;
  dno?: string; // Delivery note number
  invoice_date: string;
  due_date: string;
  lpo_number?: string;
  terms?: string;
  customer_id?: number;
  supplier_id?: number;
  tenant_id?: number;
  type: InvoiceType;
  subtotal: number;
  vat_amount: number;
  total_amount: number;
  total_paid?: number;
  status: InvoiceStatus;
  notes?: string;
  kra_integrated?: boolean;
  kra_verification_code?: string;
  kra_fiscal_receipt_number?: string;
  kra_verification_url?: string;
  kra_verification_timestamp?: string;
  kra_integration_status?: 'pending' | 'completed' | 'failed' | 'offline';
  kra_response_data?: string;
  cu_serial_number?: string;
  cu_invoice_number?: string;
  created_at: string;
  updated_at: string;

  // Relations
  items?: InvoiceItem[];
  payments?: InvoicePayment[];
  customer?: Customer;
  supplier?: Supplier;
}

export interface InvoiceFilters {
  page?: number;
  limit?: number;
  status?: InvoiceStatus | 'all';
  customer_id?: number;
  supplier_id?: number;
  type?: InvoiceType | 'all';
  start_date?: string;
  end_date?: string;
  search?: string;
  sort_by?: string;
  sort_direction?: 'asc' | 'desc';
}

export interface CreateInvoiceItemRequest {
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  vat_rate?: number;
  vat_amount?: number;
  is_vat_exempt?: boolean;
}

export interface CreateInvoiceRequest {
  invoice_date: string;
  due_date: string;
  customer_id?: number;
  supplier_id?: number;
  type: InvoiceType;
  lpo_number?: string;
  terms?: string;
  subtotal?: number;
  vat_amount?: number;
  total_amount: number;
  notes?: string;
  kra_integrated?: boolean;
  items: CreateInvoiceItemRequest[];
}

export interface UpdateInvoiceRequest {
  invoice_date?: string;
  due_date?: string;
  customer_id?: number;
  supplier_id?: number;
  type?: InvoiceType;
  lpo_number?: string;
  terms?: string;
  subtotal?: number;
  vat_amount?: number;
  total_amount?: number;
  status?: InvoiceStatus;
  notes?: string;
  kra_integrated?: boolean;
  items?: CreateInvoiceItemRequest[];
}

export interface CreateInvoicePaymentRequest {
  payment_date: string;
  amount: number;
  payment_method: string;
  reference_number?: string;
  notes?: string;
}

export interface PaginatedInvoiceResponse extends PaginatedResponse<Invoice> {
  error?: string; // Optional error field for error handling
}
