const invoiceService = require('../services/invoice.service');
const KraTimsService = require('../services/kra-tims.service');
const pdfGenerator = require('../utils/pdf-generator-pdfkit'); // Using the new PDFKit-based generator
const legacyPdfGenerator = require('../utils/pdf-generator'); // Keep the old generator as fallback
const logger = require('../utils/logger');
const { handleError } = require('../utils/error-handler');
const path = require('path');

/**
 * Create a new invoice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createInvoice = async (req, res) => {
  try {
    const { tenant_id, branch_id } = req.user;
    const invoiceData = {
      ...req.body,
      tenant_id,
      branch_id
    };

    // Create invoice
    const invoice = await invoiceService.createInvoice(invoiceData);

    // Fetch complete invoice with associations for KRA integration
    const completeInvoice = await invoiceService.getInvoiceById(invoice.id);

    if (!completeInvoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice created but could not be retrieved for KRA integration'
      });
    }

    // Register invoice with KRA only if KRA integration is enabled
    if (completeInvoice.kra_integrated) {
      try {
        // Check if raw KRA data was provided in the request
        if (req.body.kra_items || req.body.kra_customer_info) {
          // Add raw KRA data to the invoice object
          completeInvoice.raw_items = req.body.kra_items;
          completeInvoice.raw_customer_info = req.body.kra_customer_info;
        }

        // Pass the customer PIN from the request to the KRA service
        if (req.body.customer_pin && req.body.customer_pin.trim()) {
          completeInvoice.customer_pin = req.body.customer_pin.trim();
        }

        const kraService = new KraTimsService();
        const kraResponse = await kraService.registerInvoice(completeInvoice);

      if (kraResponse && kraResponse.success) {
        // Save KRA data to the invoice record
        const updateData = {
          status : "sent",
          kra_verification_code: kraResponse.verificationCode,
          kra_fiscal_receipt_number: kraResponse.fiscalReceiptNumber,
          kra_verification_url: kraResponse.qrCodeUrl || kraResponse.verificationUrl,
          kra_verification_timestamp: kraResponse.timestamp,
          kra_integration_status: kraResponse.offline ? 'offline' : 'completed',
          kra_response_data: kraResponse.fullResponseData
        };

        // Store QR code image in the response data if available
        if (kraResponse.qrCodeImage) {
          try {
            const kraData = JSON.parse(kraResponse.fullResponseData || '{}');
            kraData.qrCodeImage = kraResponse.qrCodeImage;
            updateData.kra_response_data = JSON.stringify(kraData);
          } catch (e) {
            logger.error(`Error updating KRA response data with QR code image: ${e.message}`);
          }
        }

        await invoiceService.updateInvoice(invoice.id, updateData);

        // Update the completeInvoice object with KRA data for the response
        completeInvoice.kra_verification_code = kraResponse.verificationCode;
        completeInvoice.kra_fiscal_receipt_number = kraResponse.fiscalReceiptNumber;
        completeInvoice.kra_verification_url = kraResponse.qrCodeUrl || kraResponse.verificationUrl;
        completeInvoice.kra_verification_timestamp = kraResponse.timestamp;
        completeInvoice.kra_integration_status = kraResponse.offline ? "offline" : "completed";
        completeInvoice.kra_response_data = kraResponse.fullResponseData;
        completeInvoice.status = "sent";
        logger.info(`Invoice ${invoice.id} registered with KRA successfully`);
      } else {
        logger.error(`Failed to register invoice ID ${invoice.id} with KRA`);
      }
      } catch (kraError) {
        logger.error(`Error registering invoice with KRA: ${kraError.message}`);
        // Don't throw the error, just log it - we still want to return the invoice
      }
    } else {
      // For non-KRA integrated invoices, just set status to sent
      await invoiceService.updateInvoice(invoice.id, { status: "sent" });
      completeInvoice.status = "sent";
      logger.info(`Invoice ${invoice.id} created without KRA integration`);
    }

    // Prepare enhanced response with KRA details
    const responseData = completeInvoice.toJSON();

    // Try to parse the KRA response data if it's a string
    let parsedKraData = null;
    if (responseData.kra_response_data && typeof responseData.kra_response_data === 'string') {
      try {
        parsedKraData = JSON.parse(responseData.kra_response_data);
      } catch (e) {
        logger.warn(`Failed to parse KRA response data: ${e.message}`);
      }
    }

    // Add customer_pin for backward compatibility
    if (parsedKraData && parsedKraData.customerInfo && parsedKraData.customerInfo.ClientPINnum) {
      responseData.customer_pin = parsedKraData.customerInfo.ClientPINnum;
    } else if (responseData.customer && responseData.customer.pin_number) {
      responseData.customer_pin = responseData.customer.pin_number;
    }

    // Ensure KRA details are included
    const kraDetails = {
      kra_verification_code: responseData.kra_verification_code || "",
      kra_fiscal_receipt_number: responseData.kra_fiscal_receipt_number || "",
      kra_verification_url: responseData.kra_verification_url || "",
      kra_verification_timestamp: responseData.kra_verification_timestamp || null,
      kra_integration_status: responseData.kra_integration_status || "pending",
      kra_response_data: responseData.kra_response_data || "",
      kra_qr_code_image: responseData.kra_qr_code_image || "",
    };

    // Add KRA details to the response data
    const enhancedResponseData = {
      ...responseData,
      ...kraDetails,
      // Include parsed KRA data if available
      kraData: parsedKraData
    };

    logger.info(`Invoice created successfully: ${invoice.id}`);

    return res.status(201).json({
      success: true,
      message: 'Invoice created successfully',
      data: enhancedResponseData,
    });

  } catch (error) {
    logger.error(`Error creating invoice: ${error.message}`);
    return handleError(res, error);
  }
};

/**
 * Get all invoices with pagination, filtering, and sorting
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getInvoices = async (req, res) => {
  try {
    const { tenant_id, branch_id } = req.user;
    const {
      page = 1,
      limit = 10,
      status,
      customer_id,
      supplier_id,
      type,
      search,
      start_date,
      end_date,
      sort_by,
      sort_direction
    } = req.query;

    // Validate pagination parameters
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || pageNum < 1) {
      return res.status(400).json({
        success: false,
        message: 'Invalid page parameter. Must be a positive integer.'
      });
    }

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({
        success: false,
        message: 'Invalid limit parameter. Must be a positive integer between 1 and 100.'
      });
    }

    // Build filter options
    const options = {
      tenant_id,
      branch_id,
      page: pageNum,
      limit: limitNum
    };

    // Add optional filters
    if (status) options.status = status;
    if (customer_id) options.customer_id = parseInt(customer_id, 10);
    if (supplier_id) options.supplier_id = parseInt(supplier_id, 10);
    if (type) options.type = type;
    if (search) options.search = search;

    // Add date range filter
    if (start_date && end_date) {
      try {
        options.date_range = {
          start: new Date(start_date),
          end: new Date(end_date)
        };

        // Validate dates
        if (isNaN(options.date_range.start.getTime()) || isNaN(options.date_range.end.getTime())) {
          return res.status(400).json({
            success: false,
            message: 'Invalid date format. Use YYYY-MM-DD format.'
          });
        }

        // Ensure end date is not before start date
        if (options.date_range.end < options.date_range.start) {
          return res.status(400).json({
            success: false,
            message: 'End date cannot be before start date.'
          });
        }
      } catch (dateError) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format. Use YYYY-MM-DD format.'
        });
      }
    }

    // Add sorting options
    if (sort_by) options.sort_by = sort_by;
    if (sort_direction) options.sort_direction = sort_direction;

    // Get invoices
    const invoices = await invoiceService.getInvoices(options);

    // Format response
    return res.status(200).json({
      success: true,
      data: invoices.rows,
      pagination: {
        total: invoices.count,
        page: options.page,
        limit: options.limit,
        pages: Math.ceil(invoices.count / options.limit)
      }
    });
  } catch (error) {
    logger.error(`Error in getInvoices controller: ${error.message}`);
    return handleError(res, error);
  }
};

/**
 * Get invoice by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getInvoiceById = async (req, res) => {
  try {
    const { id } = req.params;
    const invoice = await invoiceService.getInvoiceById(id);

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: invoice
    });
  } catch (error) {
    return handleError(res, error);
  }
};

/**
 * Update invoice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateInvoice = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Update invoice
    const result = await invoiceService.updateInvoice(id, updateData);

    // Check if update was successful
    if (!result.success) {
      // Determine appropriate status code based on the error message
      const statusCode = result.message.includes('not found') ? 404 : 400;

      return res.status(statusCode).json({
        success: false,
        message: result.message
      });
    }

    // Get updated invoice
    const invoice = await invoiceService.getInvoiceById(id);

    return res.status(200).json({
      success: true,
      data: invoice
    });
  } catch (error) {
    return handleError(res, error);
  }
};

/**
 * Delete invoice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteInvoice = async (req, res) => {
  try {
    const { id } = req.params;

    // Delete invoice
    const result = await invoiceService.deleteInvoice(id);

    // Check if deletion was successful
    if (!result.success) {
      // Determine appropriate status code based on the error message
      const statusCode = result.message.includes('not found') ? 404 : 400;

      return res.status(statusCode).json({
        success: false,
        message: result.message
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Invoice deleted successfully'
    });
  } catch (error) {
    return handleError(res, error);
  }
};

/**
 * Add invoice payment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addInvoicePayment = async (req, res) => {
  try {
    const { id } = req.params;
    const paymentData = {
      ...req.body,
      invoice_id: id
    };

    // Add payment
    const payment = await invoiceService.addInvoicePayment(paymentData);

    // Update invoice status based on payment
    await invoiceService.updateInvoiceStatusAfterPayment(id);

    // Get updated invoice
    const invoice = await invoiceService.getInvoiceById(id);

    return res.status(201).json({
      success: true,
      data: {
        payment,
        invoice
      }
    });
  } catch (error) {
    return handleError(res, error);
  }
};

/**
 * Get invoice payments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getInvoicePayments = async (req, res) => {
  try {
    const { id } = req.params;

    // Get payments
    const payments = await invoiceService.getInvoicePayments(id);

    return res.status(200).json({
      success: true,
      data: payments
    });
  } catch (error) {
    return handleError(res, error);
  }
};

/**
 * Generate PDF for an invoice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.generateInvoicePdf = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Generating PDF for invoice ID: ${id}`);

    const invoice = await invoiceService.getInvoiceById(id);

    if (!invoice) {
      logger.warn(`Invoice not found with ID: ${id}`);
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    logger.info(`Found invoice: ${invoice.invoice_number}`);

    // Get KRA QR code if available
    let kraQrCodeImage = null;
    if (invoice.kra_verification_url) {
      logger.info(`Getting QR code for KRA verification URL: ${invoice.kra_verification_url}`);
      try {
        const kraService = new KraTimsService();
        kraQrCodeImage = await kraService.generateQRCodeForUrl(invoice.kra_verification_url);
        logger.info(`QR code generated successfully, length: ${kraQrCodeImage ? kraQrCodeImage.length : 0} characters`);
      } catch (qrError) {
        logger.error(`Error generating QR code: ${qrError.message}`);
        // Continue without QR code
      }
    }

    // Get company information
    let companyInfo = null;
    try {
      // This is a placeholder - you would typically get this from your tenant/company settings
      companyInfo = {
        name: 'Simba Telecom',
        address: '123 Business Street, Nairobi, Kenya',
        phone: '+*********** 789',
        email: '<EMAIL>',
        logo: path.join(__dirname, '../../logo/simbatelecomlogo.png')
      };
      logger.info(`Company information loaded for PDF`);
    } catch (companyError) {
      logger.error(`Error getting company information: ${companyError.message}`);
      // Continue with default values
    }

    // Prepare data for PDF
    const pdfData = {
      ...invoice.toJSON(),
      company: companyInfo,
      kra_qr_code_image: kraQrCodeImage,
      kra_verification_timestamp: invoice.kra_verification_timestamp ? new Date(invoice.kra_verification_timestamp).toLocaleString() : null,
      kra_verification_code: invoice.kra_verification_code,
      kra_fiscal_receipt_number: invoice.kra_fiscal_receipt_number,
      // Map KRA fields to PDFKit expected field names
      cu_serial_number: invoice.kra_verification_code,
      cu_invoice_number: invoice.kra_fiscal_receipt_number,
      formatted_date: new Date(invoice.invoice_date).toLocaleDateString(),
      formatted_due_date: new Date(invoice.due_date).toLocaleDateString(),
      generation_date: new Date().toLocaleDateString()
    };

    logger.info(`Prepared data for PDF generation`);

    try {
      // Generate PDF using PDFKit
      logger.info(`Calling PDFKit PDF generator for invoice: ${invoice.invoice_number}`);
      const pdfBuffer = await pdfGenerator.generatePDF('invoice', pdfData);

      // Set response headers
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="Invoice-${invoice.invoice_number}.pdf"`);

      // Send PDF
      logger.info(`Sending PDF to client, size: ${pdfBuffer.length} bytes`);
      return res.send(pdfBuffer);
    } catch (pdfError) {
      logger.error(`Error generating PDF with PDFKit: ${pdfError.message}`);

      // Try to generate PDF using legacy generator as fallback
      try {
        logger.info(`Attempting to generate PDF with legacy generator as fallback`);
        const pdfBuffer = await legacyPdfGenerator.generatePDF('invoice', pdfData);

        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="Invoice-${invoice.invoice_number}.pdf"`);

        logger.info(`Sending legacy PDF to client as fallback`);
        return res.send(pdfBuffer);
      } catch (legacyError) {
        logger.error(`Error generating PDF with legacy generator: ${legacyError.message}`);

        // Try to generate HTML as final fallback
        try {
          logger.info(`Attempting to generate HTML as final fallback`);
          const html = await legacyPdfGenerator.generateHTML('invoice', pdfData);

          // Send HTML instead
          res.setHeader('Content-Type', 'text/html');
          res.setHeader('Content-Disposition', `inline; filename="Invoice-${invoice.invoice_number}.html"`);

          logger.info(`Sending HTML to client as final fallback`);
          return res.send(html);
        } catch (htmlError) {
          logger.error(`Error generating HTML fallback: ${htmlError.message}`);
          throw pdfError; // Throw the original error
        }
      }
    }
  } catch (error) {
    logger.error(`Error in generateInvoicePdf: ${error.message}`);
    return handleError(res, error);
  }
};