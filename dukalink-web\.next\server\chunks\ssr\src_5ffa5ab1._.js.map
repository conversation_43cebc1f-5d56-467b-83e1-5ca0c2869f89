{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,4JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess } from \"@/lib/role-utils\"; // Keep for backward compatibility\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // For MVP, prioritize role-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"role\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using role-based access (MVP approach)`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by role check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role\r\n        if (roleName === \"accountant\" || roleName === \"finance_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to finance dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/finance\");\r\n        } else if (roleName === \"stock_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to stock dashboard`);\r\n          router.replace(\"/dashboard\");\r\n\r\n          // router.replace(\"/dashboard/stock\");\r\n        } else if (\r\n          roleName === \"operations\" ||\r\n          roleName === \"operations_manager\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to operations dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/operations\");\r\n        } else if (roleName === \"float_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to float dashboard`);\r\n          // router.replace(\"/dashboard/float\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (\r\n          roleName === \"pos_operator\" ||\r\n          roleName === \"shop_attendant\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to POS dashboard`);\r\n          // router.replace(\"/dashboard/pos\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (roleName === \"company_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to company dashboard`);\r\n          // router.replace(\"/dashboard/company\");\r\n          router.replace(\"/dashboard\");\r\n        } else {\r\n          console.log(`[RoleGuard] Redirecting to main dashboard`);\r\n          router.replace(\"/dashboard\");\r\n        }\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,qNAAmD,kCAAkC;AACrF;AARA;;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;YACA,WAAW,QAAQ;YACnB;QACF;QAEA,4EAA4E;QAC5E,iEAAiE;QACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC5D,WAAW,QAAQ;YACnB;QACF,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,+DAA+D;QAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;YACnD,6CAA6C;YAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;YAEN,4CAA4C;YAC5C,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YAE3C,IAAI,YAAY;YAChB,IAAI,eAAe;YAEnB,gDAAgD;YAChD,YAAY,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YACrC,eAAe;YAEf,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;YAChD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;YAGxE,8EAA8E;YAC9E,IAAI,iBAAiB;gBACnB,2EAA2E;gBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;gBAEjE,uEAAuE;gBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;gBAGvB,gDAAgD;gBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;gBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;gBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;YAE7B;YAEA,IAAI,CAAC,WAAW;gBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;gBAGjG,sDAAsD;gBACtD,IAAI,aAAa,gBAAgB,aAAa,mBAAmB;oBAC/D,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;oBAC1D,OAAO,OAAO,CAAC;gBACf,wCAAwC;gBAC1C,OAAO,IAAI,aAAa,eAAe;oBACrC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;oBACxD,OAAO,OAAO,CAAC;gBAEf,sCAAsC;gBACxC,OAAO,IACL,aAAa,gBACb,aAAa,sBACb;oBACA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC;oBAC7D,OAAO,OAAO,CAAC;gBACf,2CAA2C;gBAC7C,OAAO,IAAI,aAAa,iBAAiB;oBACvC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;oBACxD,sCAAsC;oBACtC,OAAO,OAAO,CAAC;gBACjB,OAAO,IACL,aAAa,kBACb,aAAa,kBACb;oBACA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC;oBACtD,oCAAoC;oBACpC,OAAO,OAAO,CAAC;gBACjB,OAAO,IAAI,aAAa,iBAAiB;oBACvC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;oBAC1D,wCAAwC;oBACxC,OAAO,OAAO,CAAC;gBACjB,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,yCAAyC,CAAC;oBACvD,OAAO,OAAO,CAAC;gBACjB;YACF;YAEA,8DAA8D;YAC9D,WAAW,QAAQ;YACnB,iBAAiB;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;YAClD,8CAA8C;YAC9C,WAAW,QAAQ;QACrB;IACA,uDAAuD;IACzD,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAmC;QAEvC,IAAI,UAAU,IAAI,EAAE;YAClB,YAAY,WAAW;gBACrB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,GAAG,OAAO,mCAAmC;QAC/C;QAEA,OAAO;YACL,IAAI,WAAW,aAAa;QAC9B;IACA,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,8OAAC,sJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBAC3B,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;gBAAE;gBAAS;YAAK,IAAI;YAC3D,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACnC,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,OAAO,YAAY,WAAW;YAAE,UAAU;QAAQ,IAAI;IAC/D,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC7B,OAAO,UAAU,eAAe;IAClC,GAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;YAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAW;KAAM;AACvB;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C", "debugId": null}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;AAKO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,8OAAC,mIAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,mIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,8OAAC,uIAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;0CACd,8OAAC,uIAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,8OAAC,uIAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,8OAAC,mIAAA,CAAA,qBAAkB;sDACjB,cAAA,8OAAC,mIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,8OAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;AAdA;;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,4IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,oBAAiB;0CAChB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;;sDACf,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,mBAAgB;;kDACf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  CreditCard,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Settings,\r\n  ShoppingCart,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Inventory\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        {\r\n          title: \"Stock Cards\",\r\n          url: \"/inventory/stock-cards\",\r\n        },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"POS Management\",\r\n      url: \"/pos\",\r\n      icon: CreditCard,\r\n      isActive:\r\n        pathname.startsWith(\"/pos\") ||\r\n        pathname.startsWith(\"/sales\") ||\r\n        pathname.startsWith(\"/customers\"),\r\n      items: [\r\n        {\r\n          title: \"POS Dashboard\",\r\n          url: \"/pos\",\r\n        },\r\n        {\r\n          title: \"POS Sessions\",\r\n          url: \"/pos/sessions\",\r\n        },\r\n        {\r\n          title: \"Cash Balance\",\r\n          url: \"/dashboard/cash-balance\",\r\n        },\r\n        {\r\n          title: \"Sales\",\r\n          url: \"/sales\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Procurement\",\r\n      url: \"/procurement\",\r\n      icon: ShoppingCart,\r\n      isActive:\r\n        pathname.startsWith(\"/procurement\") ||\r\n        pathname.startsWith(\"/suppliers\"),\r\n      items: [\r\n        {\r\n          title: \"Procurement Dashboard\",\r\n          url: \"/procurement\",\r\n        },\r\n        {\r\n          title: \"Procurement Requests\",\r\n          url: \"/procurement/requests\",\r\n        },\r\n        {\r\n          title: \"Create Request\",\r\n          url: \"/procurement/requests/new\",\r\n        },\r\n        {\r\n          title: \"Procurement Receipts\",\r\n          url: \"/procurement/receipts\",\r\n        },\r\n        {\r\n          title: \"Suppliers\",\r\n          url: \"/suppliers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on role (MVP approach)\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    // Create a custom Employees section for branch managers\r\n    const employeesSection: NavItem = {\r\n      title: \"Employees\",\r\n      url: \"/employees\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        {\r\n          title: \"Manage Employees\",\r\n          url: \"/employees\",\r\n        },\r\n        {\r\n          title: \"Add Employee\",\r\n          url: \"/employees/create\",\r\n        },\r\n      ],\r\n    };\r\n\r\n    // For MVP, use role-based filtering as the default approach\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using role-based filtering (MVP approach)`\r\n    );\r\n\r\n    // Filter navigation items based on user role\r\n    const filteredItems = allItems.filter((item) => {\r\n      // For super_admin and company_admin, show all items\r\n      if (userRoleName === \"super_admin\" || userRoleName === \"company_admin\") {\r\n        console.log(`[Navigation] Admin role detected - showing all items`);\r\n        return true;\r\n      }\r\n\r\n      // For branch_admin, show most items\r\n      if (userRoleName === \"branch_admin\") {\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n        return true;\r\n      }\r\n\r\n      // For accountant, show finance-related items\r\n      if (userRoleName === \"accountant\") {\r\n        // Allow Dashboard\r\n        // Hide Tenants section\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Administration routes for accountant\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Phone Repairs\r\n        if (item.title === \"Phone Repairs\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management for viewing sales data\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide Settings for accountant\r\n        if (item.title === \"Settings\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For auditor, show reporting and read-only sections\r\n      if (userRoleName === \"auditor\") {\r\n        // Allow Reports, Banking, Expenses, Float (read-only)\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\" ||\r\n          item.title === \"Products & Inventory\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For finance_manager, show finance-related sections\r\n      if (userRoleName === \"finance_manager\") {\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For float_manager, show float-related sections\r\n      if (userRoleName === \"float_manager\") {\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Float Management, Banking, POS, and Reports\r\n        if (\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"POS Management\" ||\r\n          item.title === \"Reports\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For operations and operations_manager, show operations-related sections\r\n      if (\r\n        userRoleName === \"operations\" ||\r\n        userRoleName === \"operations_manager\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow everything else\r\n        return true;\r\n      }\r\n\r\n      // For stock_admin, show inventory-related sections\r\n      if (userRoleName === \"stock_admin\") {\r\n        // Allow Products & Inventory\r\n        if (\r\n          item.title === \"Products & Inventory\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Procurement\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // Branch Manager role removed as they don't login via the web\r\n\r\n      // For pos_operator and shop_attendant, hide admin sections\r\n      if (\r\n        userRoleName === \"pos_operator\" ||\r\n        userRoleName === \"shop_attendant\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Float Management\r\n        if (item.title === \"Float Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Banking Management\r\n        if (item.title === \"Banking Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Expenses Management\r\n        if (item.title === \"Expenses Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Products & Inventory (read-only)\r\n        if (item.title === \"Products & Inventory\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For other roles, restrict access to only essential items\r\n      console.log(\r\n        `[Navigation] Unknown role detected: ${userRoleName} - restricting access`\r\n      );\r\n\r\n      // Allow Dashboard only\r\n      if (item.title === \"Dashboard\") {\r\n        return true;\r\n      }\r\n\r\n      // Hide Settings and everything else for unknown roles\r\n      return false;\r\n    });\r\n\r\n    // Filter subitems based on role\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on role\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // For Administration, filter RBAC items for non-super_admin users\r\n          if (item.title === \"Administration\") {\r\n            // Only super_admin can see RBAC items\r\n            if (subItem.title === \"RBAC\") {\r\n              return userRoleName === \"super_admin\";\r\n            }\r\n            // All other admin roles can see other items\r\n            return true;\r\n          }\r\n\r\n          // For Settings, only show Profile for most roles\r\n          if (item.title === \"Settings\") {\r\n            // Admin roles can see all settings\r\n            if (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\"\r\n            ) {\r\n              return true;\r\n            }\r\n\r\n            // Accountant can see Profile and Payment Methods\r\n            if (userRoleName === \"accountant\") {\r\n              return (\r\n                subItem.title === \"Profile\" ||\r\n                subItem.title === \"Payment Methods\"\r\n              );\r\n            }\r\n\r\n            // Other roles can only see Profile\r\n            return subItem.title === \"Profile\";\r\n          }\r\n\r\n          // For POS Management, restrict Cash Balance to admin roles and float_manager\r\n          if (\r\n            item.title === \"POS Management\" &&\r\n            subItem.title === \"Cash Balance\"\r\n          ) {\r\n            return (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\" ||\r\n              userRoleName === \"accountant\" ||\r\n              userRoleName === \"finance_manager\" ||\r\n              userRoleName === \"float_manager\"\r\n            );\r\n          }\r\n\r\n          // For Products & Inventory, restrict Categories and Brands for non-admin roles\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Categories\") ||\r\n              subItem.title.includes(\"Brands\"))\r\n          ) {\r\n            return (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\" ||\r\n              userRoleName === \"stock_admin\"\r\n            );\r\n          }\r\n\r\n          // For Products & Inventory, restrict inventory management for shop_attendant and pos_operator\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Transfer\") ||\r\n              subItem.title.includes(\"Stock Cards\") ||\r\n              subItem.title.includes(\"Inventory Reports\") ||\r\n              subItem.title.includes(\"Excel\"))\r\n          ) {\r\n            return (\r\n              userRoleName !== \"pos_operator\" &&\r\n              userRoleName !== \"shop_attendant\"\r\n            );\r\n          }\r\n\r\n          // For Reports, restrict certain reports based on role\r\n          if (item.title === \"Reports\") {\r\n            // Finance-related reports\r\n            if (\r\n              subItem.title.includes(\"Banking\") ||\r\n              subItem.title.includes(\"Cash\") ||\r\n              subItem.title.includes(\"Float\") ||\r\n              subItem.title.includes(\"Expense\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n\r\n            // Inventory-related reports\r\n            if (\r\n              subItem.title.includes(\"Stock\") ||\r\n              subItem.title.includes(\"Inventory\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\" ||\r\n                userRoleName === \"operations\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Procurement, restrict certain operations based on role\r\n          if (item.title === \"Procurement\") {\r\n            // Creating and approving procurement requests\r\n            if (subItem.title.includes(\"Create\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Float Management, restrict certain operations based on role\r\n          if (item.title === \"Float Management\") {\r\n            // Float reconciliations\r\n            if (subItem.title.includes(\"Reconciliation\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"float_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // Allow all other subitems by default\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    // Add the Employees section at the beginning of the filtered items for branch managers\r\n    if (userRoleName === \"branch_manager\") {\r\n      return [employeesSection, ...result];\r\n    }\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;AA7BA;;;;;;;;;;;AAoEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,kNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,wMAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,kNAAA,CAAA,aAAU;gBAChB,UACE,SAAS,UAAU,CAAC,WACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,gNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sNAAA,CAAA,eAAY;gBAClB,UACE,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,uDAAuD;IACvD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,wDAAwD;QACxD,MAAM,mBAA4B;YAChC,OAAO;YACP,KAAK;YACL,MAAM,gNAAA,CAAA,YAAS;YACf,UAAU,SAAS,UAAU,CAAC;YAC9B,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QAEA,4DAA4D;QAC5D,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,0DAA0D,CAAC;QAG1F,6CAA6C;QAC7C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,oDAAoD;YACpD,IAAI,iBAAiB,iBAAiB,iBAAiB,iBAAiB;gBACtE,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC;gBAClE,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,iBAAiB,gBAAgB;gBACnC,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,6CAA6C;YAC7C,IAAI,iBAAiB,cAAc;gBACjC,kBAAkB;gBAClB,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,4CAA4C;gBAC5C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,oBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,KAAK,KAAK,KAAK,iBAAiB;oBAClC,OAAO;gBACT;gBAEA,8CAA8C;gBAC9C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,IAAI,KAAK,KAAK,KAAK,YAAY;oBAC7B,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,WAAW;gBAC9B,sDAAsD;gBACtD,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,oBACf,KAAK,KAAK,KAAK,wBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,mBAAmB;gBACtC,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,kBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,iDAAiD;YACjD,IAAI,iBAAiB,iBAAiB;gBACpC,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,IACE,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,oBACf,KAAK,KAAK,KAAK,WACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,0EAA0E;YAC1E,IACE,iBAAiB,gBACjB,iBAAiB,sBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,OAAO;YACT;YAEA,mDAAmD;YACnD,IAAI,iBAAiB,eAAe;gBAClC,6BAA6B;gBAC7B,IACE,KAAK,KAAK,KAAK,0BACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,eACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,8DAA8D;YAE9D,2DAA2D;YAC3D,IACE,iBAAiB,kBACjB,iBAAiB,kBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,IAAI,KAAK,KAAK,KAAK,oBAAoB;oBACrC,OAAO;gBACT;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,KAAK,KAAK,sBAAsB;oBACvC,OAAO;gBACT;gBAEA,2BAA2B;gBAC3B,IAAI,KAAK,KAAK,KAAK,uBAAuB;oBACxC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,yCAAyC;gBACzC,IAAI,KAAK,KAAK,KAAK,wBAAwB;oBACzC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,2DAA2D;YAC3D,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,aAAa,qBAAqB,CAAC;YAG5E,uBAAuB;YACvB,IAAI,KAAK,KAAK,KAAK,aAAa;gBAC9B,OAAO;YACT;YAEA,sDAAsD;YACtD,OAAO;QACT;QAEA,gCAAgC;QAChC,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,sCAAsC;wBACtC,IAAI,QAAQ,KAAK,KAAK,QAAQ;4BAC5B,OAAO,iBAAiB;wBAC1B;wBACA,4CAA4C;wBAC5C,OAAO;oBACT;oBAEA,iDAAiD;oBACjD,IAAI,KAAK,KAAK,KAAK,YAAY;wBAC7B,mCAAmC;wBACnC,IACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB;4BACA,OAAO;wBACT;wBAEA,iDAAiD;wBACjD,IAAI,iBAAiB,cAAc;4BACjC,OACE,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK;wBAEtB;wBAEA,mCAAmC;wBACnC,OAAO,QAAQ,KAAK,KAAK;oBAC3B;oBAEA,6EAA6E;oBAC7E,IACE,KAAK,KAAK,KAAK,oBACf,QAAQ,KAAK,KAAK,gBAClB;wBACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;oBAErB;oBAEA,+EAA+E;oBAC/E,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,iBACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,SAAS,GAClC;wBACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,8FAA8F;oBAC9F,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,eACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,kBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,wBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,QAAQ,GACjC;wBACA,OACE,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,sDAAsD;oBACtD,IAAI,KAAK,KAAK,KAAK,WAAW;wBAC5B,0BAA0B;wBAC1B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,WACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;wBAEA,4BAA4B;wBAC5B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB,wBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,6DAA6D;oBAC7D,IAAI,KAAK,KAAK,KAAK,eAAe;wBAChC,8CAA8C;wBAC9C,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,WAAW;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB;wBAErB;oBACF;oBAEA,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,oBAAoB;wBACrC,wBAAwB;wBACxB,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,mBAAmB;4BAC5C,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;oBACF;oBAEA,sCAAsC;oBACtC,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,uFAAuF;QACvF,IAAI,iBAAiB,kBAAkB;YACrC,OAAO;gBAAC;mBAAqB;aAAO;QACtC;QAEA,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,sOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,8OAAC,iIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,8OAAC,iIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 3577, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;AAJA;;;;AAMO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}, {"offset": {"line": 3679, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3744, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;AA/BA;;;;;;;;;;;;AAiCO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,8OAAC,oNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,8OAAC,sNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,8OAAC,oIAAA,CAAA,aAAU;;;;;0CAEX,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,sIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,oBAAiB;;8DAChB,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 4179, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 4311, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAXA;;;;;AAaO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;sBACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,8OAAC,sIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,8OAAC,sIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4427, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,iCAAiC;QACjC,gEAAgE;QAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC3C,WAAW,QAAQ;QACrB,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,IAAI,CAAC,eAAe;YAClB,QAAQ,uCAAuC;QACjD;QAEA,kEAAkE;QAClE,WAAW,QAAQ;QAEnB,4DAA4D;QAC5D,0DAA0D;QAC1D,IAAI,aAAa;YACf,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,+CAA+C;QAC/C,IAAI,CAAC,aAAa;QAChB,qCAAqC;QACvC;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,UAAU,IAAI,EAAE;gBAClB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB;QACF,GAAG,OAAO,mCAAmC;QAE7C,OAAO,IAAM,aAAa;IAC1B,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,8IAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;AAEA,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,8OAAC,iIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;8CAAI;;;;;;;kDACvB,8OAAC,iIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4639, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4667, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 4819, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4844, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 5069, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Switch({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\r\n  return (\r\n    <SwitchPrimitive.Root\r\n      data-slot=\"switch\"\r\n      className={cn(\r\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SwitchPrimitive.Thumb\r\n        data-slot=\"switch-thumb\"\r\n        className={cn(\r\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\r\n        )}\r\n      />\r\n    </SwitchPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 5202, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/customers/api/customer-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  Customer,\r\n  CreateCustomerRequest,\r\n  UpdateCustomerRequest,\r\n  CustomerSalesResponse,\r\n  CustomerFilters,\r\n  PaginatedCustomerResponse,\r\n} from \"@/types\";\r\n\r\n/**\r\n * Customer Service\r\n * Handles API calls for customers\r\n */\r\nconst customerService = {\r\n  /**\r\n   * Get all customers with optional filters\r\n   */\r\n  getCustomers: async (\r\n    filters?: CustomerFilters\r\n  ): Promise<PaginatedCustomerResponse> => {\r\n    const response = await apiClient.get<PaginatedCustomerResponse>(\"/customers\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get a customer by ID\r\n   */\r\n  getCustomerById: async (id: number): Promise<Customer> => {\r\n    const response = await apiClient.get<Customer>(`/customers/${id}`);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Create a new customer\r\n   */\r\n  createCustomer: async (\r\n    customer: CreateCustomerRequest\r\n  ): Promise<Customer> => {\r\n    const response = await apiClient.post<Customer>(\"/customers\", customer);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Update a customer\r\n   */\r\n  updateCustomer: async (\r\n    id: number,\r\n    customer: UpdateCustomerRequest\r\n  ): Promise<Customer> => {\r\n    const response = await apiClient.put<Customer>(\r\n      `/customers/${id}`,\r\n      customer\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Delete a customer\r\n   */\r\n  deleteCustomer: async (id: number): Promise<void> => {\r\n    await apiClient.delete(`/customers/${id}`);\r\n  },\r\n\r\n  /**\r\n   * Get sales for a customer\r\n   */\r\n  getCustomerSales: async (id: number): Promise<CustomerSalesResponse> => {\r\n    const response = await apiClient.get<CustomerSalesResponse>(\r\n      `/customers/${id}/sales`\r\n    );\r\n    return response;\r\n  },\r\n};\r\n\r\nexport default customerService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAUA;;;CAGC,GACD,MAAM,kBAAkB;IACtB;;GAEC,GACD,cAAc,OACZ;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAA4B,cAAc;YAC5E,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI;QACjE,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OACd;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAW,cAAc;QAC9D,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OACd,IACA;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,WAAW,EAAE,IAAI,EAClB;QAEF,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,MAAM,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAC3C;IAEA;;GAEC,GACD,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC;QAE1B,OAAO;IACT;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 5256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/customers/hooks/use-customers.ts"], "sourcesContent": ["import {\r\n  CreateCustomerRequest,\r\n  CustomerFilters,\r\n  UpdateCustomerRequest,\r\n} from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport customerService from \"../api/customer-service\";\r\n\r\n/**\r\n * Hook to fetch all customers with optional filters\r\n */\r\nexport const useCustomers = (filters?: CustomerFilters) => {\r\n  return useQuery({\r\n    queryKey: [\"customers\", filters],\r\n    queryFn: () => customerService.getCustomers(filters),\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch a single customer by ID\r\n */\r\nexport const useCustomer = (id: number) => {\r\n  return useQuery({\r\n    queryKey: [\"customer\", id],\r\n    queryFn: () => customerService.getCustomerById(id),\r\n    enabled: !!id,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch sales for a customer\r\n */\r\nexport const useCustomerSales = (id: number) => {\r\n  return useQuery({\r\n    queryKey: [\"customer\", id, \"sales\"],\r\n    queryFn: () => customerService.getCustomerSales(id),\r\n    enabled: !!id,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to create a new customer\r\n */\r\nexport const useCreateCustomer = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (customer: CreateCustomerRequest) =>\r\n      customerService.createCustomer(customer),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"customers\"] });\r\n      toast.success(\"Customer created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"An error occurred while creating the customer\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update a customer\r\n */\r\nexport const useUpdateCustomer = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (customer: UpdateCustomerRequest) =>\r\n      customerService.updateCustomer(id, customer),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"customers\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"customer\", id] });\r\n      toast.success(\"Customer updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"An error occurred while updating the customer\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete a customer\r\n */\r\nexport const useDeleteCustomer = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => customerService.deleteCustomer(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"customers\"] });\r\n      toast.success(\"Customer deleted successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"An error occurred while deleting the customer\"\r\n      );\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAKA;AAAA;AAAA;AACA;AACA;;;;AAKO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAa;SAAQ;QAChC,SAAS,IAAM,0JAAA,CAAA,UAAe,CAAC,YAAY,CAAC;IAC9C;AACF;AAKO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,SAAS,IAAM,0JAAA,CAAA,UAAe,CAAC,eAAe,CAAC;QAC/C,SAAS,CAAC,CAAC;IACb;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;YAAI;SAAQ;QACnC,SAAS,IAAM,0JAAA,CAAA,UAAe,CAAC,gBAAgB,CAAC;QAChD,SAAS,CAAC,CAAC;IACb;AACF;AAKO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,WACX,0JAAA,CAAA,UAAe,CAAC,cAAc,CAAC;QACjC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;QAErB;IACF;AACF;AAKO,MAAM,oBAAoB,CAAC;IAChC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,WACX,0JAAA,CAAA,UAAe,CAAC,cAAc,CAAC,IAAI;QACrC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;YAC3D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;QAErB;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,0JAAA,CAAA,UAAe,CAAC,cAAc,CAAC;QAC3D,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;QAErB;IACF;AACF", "debugId": null}}, {"offset": {"line": 5365, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/api/supplier-service.ts"], "sourcesContent": ["import apiClient from '@/lib/api-client';\r\nimport { Supplier, SupplierFormData } from '../types/supplier';\r\n\r\nexport const supplierService = {\r\n  /**\r\n   * Get all suppliers with optional filtering\r\n   */\r\n  getSuppliers: async (params?: {\r\n    search?: string;\r\n    page?: number;\r\n    limit?: number;\r\n  }): Promise<{ data: Supplier[]; pagination: any }> => {\r\n    try {\r\n      console.log('Fetching suppliers with params:', params);\r\n      const response = await apiClient.get<any>('/suppliers', { params });\r\n      console.log('Suppliers API response:', response);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching suppliers:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get supplier by ID\r\n   */\r\n  getSupplierById: async (id: number): Promise<Supplier> => {\r\n    try {\r\n      const response = await apiClient.get<Supplier>(`/suppliers/${id}`);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error fetching supplier with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new supplier\r\n   */\r\n  createSupplier: async (data: SupplierFormData): Promise<Supplier> => {\r\n    try {\r\n      // Validate required fields before sending to API\r\n      if (!data.name || !data.krapin) {\r\n        throw new Error('Name and KRA PIN are required fields');\r\n      }\r\n\r\n      // Trim whitespace from string fields\r\n      const cleanedData = Object.entries(data).reduce((acc, [key, value]) => {\r\n        acc[key] = typeof value === 'string' ? value.trim() : value;\r\n        return acc;\r\n      }, {} as Record<string, any>);\r\n\r\n      console.log('Creating supplier with data:', cleanedData);\r\n      const response = await apiClient.post<Supplier>('/suppliers', cleanedData);\r\n      console.log('Supplier creation response:', response);\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error('Error creating supplier:', error);\r\n      // Enhance error message with more details if available\r\n      if (error.response?.data?.message) {\r\n        error.message = error.response.data.message;\r\n      } else if (!error.message) {\r\n        error.message = 'Failed to create supplier. Please check your input and try again.';\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update an existing supplier\r\n   */\r\n  updateSupplier: async (id: number, data: SupplierFormData): Promise<Supplier> => {\r\n    try {\r\n      const response = await apiClient.put<Supplier>(`/suppliers/${id}`, data);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error updating supplier with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a supplier\r\n   */\r\n  deleteSupplier: async (id: number): Promise<void> => {\r\n    try {\r\n      await apiClient.delete(`/suppliers/${id}`);\r\n    } catch (error) {\r\n      console.error(`Error deleting supplier with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAc,OAAO;QAKnB,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,cAAc;gBAAE;YAAO;YACjE,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI;YACjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,iDAAiD;YACjD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,MAAM,EAAE;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,qCAAqC;YACrC,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;gBAChE,GAAG,CAAC,IAAI,GAAG,OAAO,UAAU,WAAW,MAAM,IAAI,KAAK;gBACtD,OAAO;YACT,GAAG,CAAC;YAEJ,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAW,cAAc;YAC9D,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,uDAAuD;YACvD,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;gBACzB,MAAM,OAAO,GAAG;YAClB;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO,IAAY;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI,EAAE;YACnE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 5453, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/hooks/use-suppliers.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { supplierService } from '../api/supplier-service';\r\nimport { Supplier, SupplierFormData } from '../types/supplier';\r\nimport { useToast } from '@/components/ui/use-toast';\r\n\r\n/**\r\n * Hook to fetch suppliers with optional filtering\r\n */\r\nexport const useSuppliers = (params?: {\r\n  search?: string;\r\n  page?: number;\r\n  limit?: number;\r\n}) => {\r\n  return useQuery({\r\n    queryKey: ['suppliers', params],\r\n    queryFn: async () => {\r\n      try {\r\n        const response = await supplierService.getSuppliers(params);\r\n        console.log('useSuppliers response:', response);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('Error in useSuppliers:', error);\r\n        throw error;\r\n      }\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch a supplier by ID\r\n */\r\nexport const useSupplier = (id: number) => {\r\n  return useQuery({\r\n    queryKey: ['supplier', id],\r\n    queryFn: () => supplierService.getSupplierById(id),\r\n    enabled: !!id,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to create a new supplier\r\n */\r\nexport const useCreateSupplier = () => {\r\n  const queryClient = useQueryClient();\r\n  const { toast } = useToast();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: SupplierFormData) => supplierService.createSupplier(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: ['suppliers'] });\r\n      toast.success('Supplier created successfully', {\r\n        description: 'The supplier has been added to your list',\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      console.error('Supplier creation error:', error);\r\n      toast.error('Failed to create supplier', {\r\n        description: error.message || 'Please check your input and try again',\r\n      });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update an existing supplier\r\n */\r\nexport const useUpdateSupplier = () => {\r\n  const queryClient = useQueryClient();\r\n  const { toast } = useToast();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: number; data: SupplierFormData }) =>\r\n      supplierService.updateSupplier(id, data),\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: ['suppliers'] });\r\n      queryClient.invalidateQueries({ queryKey: ['supplier', variables.id] });\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Supplier updated successfully',\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast({\r\n        title: 'Error',\r\n        description: error.message || 'Failed to update supplier',\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete a supplier\r\n */\r\nexport const useDeleteSupplier = () => {\r\n  const queryClient = useQueryClient();\r\n  const { toast } = useToast();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => supplierService.deleteSupplier(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: ['suppliers'] });\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Supplier deleted successfully',\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast({\r\n        title: 'Error',\r\n        description: error.message || 'Failed to delete supplier',\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;;;;AAKO,MAAM,eAAe,CAAC;IAK3B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAa;SAAO;QAC/B,SAAS;YACP,IAAI;gBACF,MAAM,WAAW,MAAM,0JAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;gBACpD,QAAQ,GAAG,CAAC,0BAA0B;gBACtC,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM;YACR;QACF;IACF;AACF;AAKO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,SAAS,IAAM,0JAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;QAC/C,SAAS,CAAC,CAAC;IACb;AACF;AAKO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IAEzB,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA2B,0JAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;QACvE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,MAAM,OAAO,CAAC,iCAAiC;gBAC7C,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,KAAK,CAAC,6BAA6B;gBACvC,aAAa,MAAM,OAAO,IAAI;YAChC;QACF;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IAEzB,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAA0C,GAC/D,0JAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI;QACrC,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY,UAAU,EAAE;iBAAC;YAAC;YACrE,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACX;QACF;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD;IAEzB,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,0JAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;QAC3D,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACX;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 5581, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row gap-2\",\r\n        month: \"flex flex-col gap-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center w-full\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"flex items-center gap-1\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-x-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"size-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start:\r\n          \"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_range_end:\r\n          \"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar }\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACoC;IACvC,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAE5D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;QAE/D;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5661, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 5730, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/invoices/hooks/use-vat-rates.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\r\nimport apiClient from \"@/lib/api-client\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport interface VatRate {\r\n  id: number;\r\n  name: string;\r\n  rate: number;\r\n  code?: string;\r\n  is_default: boolean;\r\n  is_active: boolean;\r\n  tenant_id: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport function useVatRates() {\r\n  return useQuery({\r\n    queryKey: [\"vat-rates\"],\r\n    queryFn: async () => {\r\n      try {\r\n        const response = await apiClient.get<VatRate[]>(\"/vat-rates\");\r\n        // Ensure we always return an array, even if the response is undefined\r\n        return response || [];\r\n      } catch (error: any) {\r\n        console.error(\"Error fetching VAT rates:\", error);\r\n        toast.error(\"Failed to load VAT rates\");\r\n        // Return an empty array instead of undefined\r\n        return [];\r\n      }\r\n    },\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    // Provide a fallback value to prevent undefined errors\r\n    placeholderData: [],\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAcO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAY;QACvB,SAAS;YACP,IAAI;gBACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAY;gBAChD,sEAAsE;gBACtE,OAAO,YAAY,EAAE;YACvB,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,6CAA6C;gBAC7C,OAAO,EAAE;YACX;QACF;QACA,WAAW,IAAI,KAAK;QACpB,uDAAuD;QACvD,iBAAiB,EAAE;IACrB;AACF", "debugId": null}}, {"offset": {"line": 5767, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/invoices/components/invoice-form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useForm, useFieldArray } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport * as z from \"zod\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { CreateInvoiceRequest, Invoice, UpdateInvoiceRequest } from \"../types\";\r\nimport { useCustomers } from \"@/features/customers/hooks/use-customers\";\r\nimport { useSuppliers } from \"@/features/inventory/hooks/use-suppliers\";\r\nimport { Trash2, Plus } from \"lucide-react\";\r\nimport { format } from \"date-fns\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { CalendarIcon } from \"lucide-react\";\r\nimport { useVatRates } from \"../hooks/use-vat-rates\";\r\n\r\n// Define the form schema\r\nconst invoiceFormSchema = z\r\n  .object({\r\n    type: z.enum([\"customer\", \"supplier\"]),\r\n    customer_id: z.number().optional().nullable(),\r\n    supplier_id: z.number().optional().nullable(),\r\n    invoice_date: z.date(),\r\n    due_date: z.date(),\r\n    lpo_number: z.string().optional(),\r\n    terms: z.string().optional(),\r\n    notes: z.string().optional(),\r\n    kra_integrated: z.boolean().default(false),\r\n    items: z.array(\r\n      z.object({\r\n        description: z.string().min(1, \"Description is required\"),\r\n        quantity: z.number().min(0.01, \"Quantity must be greater than 0\"),\r\n        unit_price: z.number().min(0, \"Unit price must be a valid number\"),\r\n        total_price: z.number().min(0, \"Total price must be a valid number\"),\r\n        vat_rate: z.number().optional().nullable(),\r\n        vat_amount: z.number().optional().nullable(),\r\n        is_vat_exempt: z.boolean().default(false),\r\n      })\r\n    ).min(1, \"At least one item is required\"),\r\n    subtotal: z.number().min(0),\r\n    vat_amount: z.number().min(0),\r\n    total_amount: z.number().min(0),\r\n  })\r\n  .refine(\r\n    (data) => {\r\n      // If type is customer, customer_id must be provided\r\n      if (data.type === \"customer\") {\r\n        return !!data.customer_id;\r\n      }\r\n      // If type is supplier, supplier_id must be provided\r\n      if (data.type === \"supplier\") {\r\n        return !!data.supplier_id;\r\n      }\r\n      return true;\r\n    },\r\n    {\r\n      message: \"Please select a customer or supplier based on invoice type\",\r\n      path: [\"customer_id\", \"supplier_id\"], // This will show the error on both fields\r\n    }\r\n  );\r\n\r\ntype InvoiceFormValues = z.infer<typeof invoiceFormSchema>;\r\n\r\ninterface InvoiceFormProps {\r\n  invoice?: Invoice;\r\n  onSubmit: (data: CreateInvoiceRequest | UpdateInvoiceRequest) => void;\r\n  isSubmitting: boolean;\r\n}\r\n\r\nexport function InvoiceForm({\r\n  invoice,\r\n  onSubmit,\r\n  isSubmitting,\r\n}: InvoiceFormProps) {\r\n  const router = useRouter();\r\n  const { data: customersData } = useCustomers();\r\n  const { data: suppliersData } = useSuppliers();\r\n  const customers = customersData?.data || [];\r\n  const suppliers = suppliersData?.data || [];\r\n\r\n  // Initialize form with default values or existing invoice data\r\n  const form = useForm<InvoiceFormValues>({\r\n    resolver: zodResolver(invoiceFormSchema),\r\n    defaultValues: invoice\r\n      ? {\r\n          type: invoice.type,\r\n          customer_id: invoice.customer_id || null,\r\n          supplier_id: invoice.supplier_id || null,\r\n          invoice_date: new Date(invoice.invoice_date),\r\n          due_date: new Date(invoice.due_date),\r\n          lpo_number: invoice.lpo_number || \"\",\r\n          terms: invoice.terms || \"\",\r\n          notes: invoice.notes || \"\",\r\n          kra_integrated: invoice.kra_integrated || false,\r\n          items: invoice.items?.map((item) => ({\r\n            description: item.description || \"\",\r\n            quantity: Number(item.quantity) || 0,\r\n            unit_price: Number(item.unit_price) || 0,\r\n            total_price: Number(item.total_price) || 0,\r\n            vat_rate: item.vat_rate !== undefined ? Number(item.vat_rate) || 0 : 0,\r\n            vat_amount: item.vat_amount !== undefined ? Number(item.vat_amount) || 0 : null,\r\n            is_vat_exempt: item.is_vat_exempt === true,\r\n          })) || [],\r\n          subtotal: Number(invoice.subtotal) || 0,\r\n          vat_amount: Number(invoice.vat_amount) || 0,\r\n          total_amount: Number(invoice.total_amount) || 0,\r\n        }\r\n      : {\r\n          type: \"customer\",\r\n          customer_id: null,\r\n          supplier_id: null,\r\n          invoice_date: new Date(),\r\n          due_date: new Date(),\r\n          lpo_number: \"\",\r\n          terms: \"\",\r\n          notes: \"\",\r\n          kra_integrated: false,\r\n          items: [\r\n            {\r\n              description: \"\",\r\n              quantity: 1,\r\n              unit_price: 0,\r\n              total_price: 0,\r\n              vat_rate: null,\r\n              vat_amount: null,\r\n              is_vat_exempt: false,\r\n            },\r\n          ],\r\n          subtotal: 0,\r\n          vat_amount: 0,\r\n          total_amount: 0,\r\n        },\r\n  });\r\n\r\n  const { fields, append, remove } = useFieldArray({\r\n    control: form.control,\r\n    name: \"items\",\r\n  });\r\n\r\n  // Watch form values for calculations\r\n  const watchItems = form.watch(\"items\");\r\n  const watchType = form.watch(\"type\");\r\n\r\n  // Calculate totals when items change\r\n  useEffect(() => {\r\n    if (watchItems) {\r\n      // Calculate total amount (sum of all item total prices)\r\n      const totalAmount = watchItems.reduce(\r\n        (sum, item) => sum + (item.total_price || 0),\r\n        0\r\n      );\r\n\r\n      // Calculate total VAT amount (sum of all item VAT amounts)\r\n      const vatAmount = watchItems.reduce(\r\n        (sum, item) => sum + (item.vat_amount || 0),\r\n        0\r\n      );\r\n\r\n      // Calculate vatable amount (total minus VAT)\r\n      const vatableAmount = totalAmount - vatAmount;\r\n\r\n      // Update form values\r\n      form.setValue(\"subtotal\", vatableAmount); // Renamed to \"vatable amount\" in UI\r\n      form.setValue(\"vat_amount\", vatAmount);\r\n      form.setValue(\"total_amount\", totalAmount);\r\n    }\r\n  }, [watchItems, form]);\r\n\r\n  // Handle form submission\r\n  const handleSubmit = (values: InvoiceFormValues) => {\r\n    // Create a base object with common fields\r\n    const baseData = {\r\n      type: values.type,\r\n      invoice_date: format(values.invoice_date, \"yyyy-MM-dd\"),\r\n      due_date: format(values.due_date, \"yyyy-MM-dd\"),\r\n      lpo_number: values.lpo_number,\r\n      terms: values.terms,\r\n      notes: values.notes,\r\n      kra_integrated: values.kra_integrated, // Include KRA integration flag\r\n      subtotal: values.subtotal,\r\n      vat_amount: values.vat_amount,\r\n      total_amount: values.total_amount,\r\n      items: values.items.map((item) => ({\r\n        ...item,\r\n        vat_rate: item.vat_rate || undefined,\r\n        vat_amount: item.vat_amount || undefined,\r\n      })),\r\n    };\r\n\r\n    // Add the appropriate ID field based on invoice type\r\n    const formattedData = {\r\n      ...baseData,\r\n      ...(values.type === \"customer\" && values.customer_id ? { customer_id: values.customer_id } : {}),\r\n      ...(values.type === \"supplier\" && values.supplier_id ? { supplier_id: values.supplier_id } : {}),\r\n    };\r\n\r\n    console.log(\"Submitting invoice data:\", formattedData);\r\n    onSubmit(formattedData);\r\n  };\r\n\r\n  // Fetch VAT rates\r\n  const { data: vatRates = [] } = useVatRates();\r\n\r\n  // Calculate item total when quantity or unit price changes\r\n  const calculateItemTotal = (index: number) => {\r\n    const item = form.getValues(`items.${index}`);\r\n\r\n    // Ensure values are numbers\r\n    const quantity = typeof item.quantity === 'string'\r\n      ? parseFloat(item.quantity) || 0\r\n      : item.quantity || 0;\r\n\r\n    const unitPrice = typeof item.unit_price === 'string'\r\n      ? parseFloat(item.unit_price) || 0\r\n      : item.unit_price || 0;\r\n\r\n    // Calculate total price from quantity and unit price\r\n    const totalPrice = quantity * unitPrice;\r\n\r\n    // Calculate VAT only if KRA integrated\r\n    let vatAmount = 0;\r\n    let isVatExempt = false;\r\n    const kraIntegrated = form.getValues(\"kra_integrated\");\r\n\r\n    if (kraIntegrated) {\r\n      // Ensure VAT rate is always a number (0 if not provided)\r\n      const vatRate = typeof item.vat_rate === 'string'\r\n        ? parseFloat(item.vat_rate) || 0\r\n        : typeof item.vat_rate === 'number'\r\n        ? item.vat_rate\r\n        : 0;\r\n\r\n      // If VAT rate is 0, mark as exempt\r\n      isVatExempt = vatRate === 0;\r\n\r\n      if (!isVatExempt) {\r\n        vatAmount = totalPrice * (vatRate / 100);\r\n      }\r\n\r\n      // Always set VAT rate to a number (0 for exempt, actual rate for taxable)\r\n      form.setValue(`items.${index}.vat_rate`, vatRate);\r\n    } else {\r\n      // For non-KRA invoices, always exempt with 0 VAT rate\r\n      isVatExempt = true;\r\n      form.setValue(`items.${index}.vat_rate`, 0);\r\n    }\r\n\r\n    // Set the calculated values with parsed numbers\r\n    form.setValue(`items.${index}.total_price`, parseFloat(totalPrice.toFixed(2)));\r\n    form.setValue(`items.${index}.vat_amount`, parseFloat(vatAmount.toFixed(2)));\r\n    form.setValue(`items.${index}.is_vat_exempt`, isVatExempt);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-6\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          {/* Invoice Type */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"type\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel className=\"flex items-center gap-1\">\r\n                  Invoice Type\r\n                  <span className=\"text-red-500\">*</span>\r\n                </FormLabel>\r\n                <Select\r\n                  onValueChange={field.onChange}\r\n                  defaultValue={field.value}\r\n                >\r\n                  <FormControl>\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select invoice type\" />\r\n                    </SelectTrigger>\r\n                  </FormControl>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"customer\">Customer Invoice</SelectItem>\r\n                    <SelectItem value=\"supplier\">Supplier Invoice</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n                <FormDescription>\r\n                  Select whether this is a customer or supplier invoice\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* Customer or Supplier */}\r\n          {watchType === \"customer\" ? (\r\n            <FormField\r\n              control={form.control}\r\n              name=\"customer_id\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel className=\"flex items-center gap-1\">\r\n                    Customer\r\n                    <span className=\"text-red-500\">*</span>\r\n                  </FormLabel>\r\n                  <Select\r\n                    onValueChange={(value) => field.onChange(parseInt(value))}\r\n                    value={field.value?.toString() || \"\"}\r\n                  >\r\n                    <FormControl>\r\n                      <SelectTrigger>\r\n                        <SelectValue placeholder=\"Select customer\" />\r\n                      </SelectTrigger>\r\n                    </FormControl>\r\n                    <SelectContent>\r\n                      {customers.map((customer) => (\r\n                        <SelectItem\r\n                          key={customer.id}\r\n                          value={customer.id.toString()}\r\n                        >\r\n                          {customer.name}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                  <FormDescription>\r\n                    Customer is required for customer invoices\r\n                  </FormDescription>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          ) : (\r\n            <FormField\r\n              control={form.control}\r\n              name=\"supplier_id\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel className=\"flex items-center gap-1\">\r\n                    Supplier\r\n                    <span className=\"text-red-500\">*</span>\r\n                  </FormLabel>\r\n                  <Select\r\n                    onValueChange={(value) => field.onChange(parseInt(value))}\r\n                    value={field.value?.toString() || \"\"}\r\n                  >\r\n                    <FormControl>\r\n                      <SelectTrigger>\r\n                        <SelectValue placeholder=\"Select supplier\" />\r\n                      </SelectTrigger>\r\n                    </FormControl>\r\n                    <SelectContent>\r\n                      {suppliers.map((supplier) => (\r\n                        <SelectItem\r\n                          key={supplier.id}\r\n                          value={supplier.id.toString()}\r\n                        >\r\n                          {supplier.name}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                  <FormDescription>\r\n                    Supplier is required for supplier invoices\r\n                  </FormDescription>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          )}\r\n\r\n          {/* Invoice Date */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"invoice_date\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"flex flex-col\">\r\n                <FormLabel>Invoice Date</FormLabel>\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <FormControl>\r\n                      <Button\r\n                        variant={\"outline\"}\r\n                        className={cn(\r\n                          \"w-full pl-3 text-left font-normal\",\r\n                          !field.value && \"text-muted-foreground\"\r\n                        )}\r\n                      >\r\n                        {field.value ? (\r\n                          format(field.value, \"PPP\")\r\n                        ) : (\r\n                          <span>Pick a date</span>\r\n                        )}\r\n                        <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\r\n                      </Button>\r\n                    </FormControl>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                    <Calendar\r\n                      mode=\"single\"\r\n                      selected={field.value}\r\n                      onSelect={field.onChange}\r\n                      initialFocus\r\n                    />\r\n                  </PopoverContent>\r\n                </Popover>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* Due Date */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"due_date\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"flex flex-col\">\r\n                <FormLabel>Due Date</FormLabel>\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <FormControl>\r\n                      <Button\r\n                        variant={\"outline\"}\r\n                        className={cn(\r\n                          \"w-full pl-3 text-left font-normal\",\r\n                          !field.value && \"text-muted-foreground\"\r\n                        )}\r\n                      >\r\n                        {field.value ? (\r\n                          format(field.value, \"PPP\")\r\n                        ) : (\r\n                          <span>Pick a date</span>\r\n                        )}\r\n                        <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\r\n                      </Button>\r\n                    </FormControl>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                    <Calendar\r\n                      mode=\"single\"\r\n                      selected={field.value}\r\n                      onSelect={field.onChange}\r\n                      initialFocus\r\n                    />\r\n                  </PopoverContent>\r\n                </Popover>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* LPO Number */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"lpo_number\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>LPO Number</FormLabel>\r\n                <FormControl>\r\n                  <Input placeholder=\"LPO-12345\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* Terms */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"terms\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Terms</FormLabel>\r\n                <FormControl>\r\n                  <Input placeholder=\"Net 30\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* KRA Integration Toggle */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"kra_integrated\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-4\">\r\n                <div className=\"space-y-0.5\">\r\n                  <FormLabel className=\"text-base\">\r\n                    KRA Integration\r\n                  </FormLabel>\r\n                  <FormDescription>\r\n                    Enable KRA integration for this invoice (includes VAT calculations)\r\n                  </FormDescription>\r\n                </div>\r\n                <FormControl>\r\n                  <Switch\r\n                    checked={field.value}\r\n                    onCheckedChange={field.onChange}\r\n                  />\r\n                </FormControl>\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        {/* Invoice Items */}\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <h3 className=\"text-lg font-medium\">Invoice Items</h3>\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() =>\r\n                append({\r\n                  description: \"\",\r\n                  quantity: 1,\r\n                  unit_price: 0,\r\n                  total_price: 0,\r\n                  vat_rate: null,\r\n                  vat_amount: null,\r\n                  is_vat_exempt: false,\r\n                })\r\n              }\r\n            >\r\n              <Plus className=\"mr-2 h-4 w-4\" /> Add Item\r\n            </Button>\r\n          </div>\r\n\r\n          {fields.map((field, index) => (\r\n            <Card key={field.id}>\r\n              <CardContent className=\"pt-6\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\r\n                  {/* Description */}\r\n                  <FormField\r\n                    control={form.control}\r\n                    name={`items.${index}.description`}\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Description</FormLabel>\r\n                        <FormControl>\r\n                          <Input placeholder=\"Item description\" {...field} />\r\n                        </FormControl>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\">\r\n                  {/* Quantity */}\r\n                  <FormField\r\n                    control={form.control}\r\n                    name={`items.${index}.quantity`}\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Quantity</FormLabel>\r\n                        <FormControl>\r\n                          <Input\r\n                            type=\"number\"\r\n                            step=\"0.01\"\r\n                            className=\"[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none\"\r\n                            {...field}\r\n                            onChange={(e) => {\r\n                              field.onChange(parseFloat(e.target.value) || 0);\r\n                              calculateItemTotal(index);\r\n                            }}\r\n                          />\r\n                        </FormControl>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  {/* Unit Price */}\r\n                  <FormField\r\n                    control={form.control}\r\n                    name={`items.${index}.unit_price`}\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Unit Price</FormLabel>\r\n                        <FormControl>\r\n                          <Input\r\n                            type=\"number\"\r\n                            step=\"0.01\"\r\n                            className=\"[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none\"\r\n                            {...field}\r\n                            onChange={(e) => {\r\n                              field.onChange(parseFloat(e.target.value) || 0);\r\n                              calculateItemTotal(index);\r\n                            }}\r\n                          />\r\n                        </FormControl>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  {/* VAT Rate - Only show if KRA integrated */}\r\n                  {form.watch(\"kra_integrated\") && (\r\n                    <FormField\r\n                      control={form.control}\r\n                      name={`items.${index}.vat_rate`}\r\n                      render={({ field }) => {\r\n                        const isVatExempt = form.getValues(`items.${index}.is_vat_exempt`);\r\n\r\n                        return (\r\n                          <FormItem>\r\n                            <FormLabel>VAT Rate (%)</FormLabel>\r\n                            <FormControl>\r\n                              <Input\r\n                                type=\"number\"\r\n                                step=\"0.01\"\r\n                                className=\"[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none\"\r\n                                {...field}\r\n                                onChange={(e) => {\r\n                                  const value = e.target.value !== \"\" ? parseFloat(e.target.value) : 0;\r\n                                  field.onChange(value);\r\n                                  calculateItemTotal(index);\r\n                                }}\r\n                                value={field.value !== null && field.value !== undefined ? field.value : \"\"}\r\n                                placeholder=\"Enter VAT rate (0 for exempt)\"\r\n                              />\r\n                            </FormControl>\r\n                            <FormDescription>\r\n                              {isVatExempt ? \"VAT exempt (0%)\" : \"VAT rate percentage (0 for exempt)\"}\r\n                            </FormDescription>\r\n                            <FormMessage />\r\n                          </FormItem>\r\n                        );\r\n                      }}\r\n                    />\r\n                  )}\r\n\r\n\r\n                  {/* Total Price (calculated) */}\r\n                  <FormField\r\n                    control={form.control}\r\n                    name={`items.${index}.total_price`}\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Total Price</FormLabel>\r\n                        <FormControl>\r\n                          <Input\r\n                            type=\"number\"\r\n                            step=\"0.01\"\r\n                            {...field}\r\n                            readOnly\r\n                            className=\"bg-gray-50\"\r\n                          />\r\n                        </FormControl>\r\n                        <FormDescription>\r\n                          Calculated: Quantity × Unit Price\r\n                        </FormDescription>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </div>\r\n\r\n                {/* Remove Item Button */}\r\n                {fields.length > 1 && (\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"destructive\"\r\n                    size=\"sm\"\r\n                    onClick={() => remove(index)}\r\n                    className=\"mt-2\"\r\n                  >\r\n                    <Trash2 className=\"mr-2 h-4 w-4\" /> Remove Item\r\n                  </Button>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Notes */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"notes\"\r\n          render={({ field }) => (\r\n            <FormItem>\r\n              <FormLabel>Notes</FormLabel>\r\n              <FormControl>\r\n                <Textarea\r\n                  placeholder=\"Additional notes or information\"\r\n                  {...field}\r\n                  rows={3}\r\n                />\r\n              </FormControl>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Totals */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <FormField\r\n            control={form.control}\r\n            name=\"subtotal\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Vatable Amount</FormLabel>\r\n                <FormControl>\r\n                  <Input\r\n                    type=\"number\"\r\n                    step=\"0.01\"\r\n                    {...field}\r\n                    readOnly\r\n                  />\r\n                </FormControl>\r\n                <FormDescription>\r\n                  Total amount minus VAT\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          <FormField\r\n            control={form.control}\r\n            name=\"vat_amount\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>VAT Amount</FormLabel>\r\n                <FormControl>\r\n                  <Input\r\n                    type=\"number\"\r\n                    step=\"0.01\"\r\n                    {...field}\r\n                    readOnly\r\n                    className=\"bg-gray-50\"\r\n                  />\r\n                </FormControl>\r\n                <FormDescription>\r\n                  Calculated based on VAT rates\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          <FormField\r\n            control={form.control}\r\n            name=\"total_amount\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Total Amount</FormLabel>\r\n                <FormControl>\r\n                  <Input\r\n                    type=\"number\"\r\n                    step=\"0.01\"\r\n                    {...field}\r\n                    readOnly\r\n                    className=\"font-bold bg-gray-50\"\r\n                  />\r\n                </FormControl>\r\n                <FormDescription>\r\n                  Sum of all item totals\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        {/* Form Actions */}\r\n        <div className=\"flex justify-end gap-4\">\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={() => router.back()}\r\n            disabled={isSubmitting}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button type=\"submit\" disabled={isSubmitting}>\r\n            {isSubmitting ? \"Saving...\" : invoice ? \"Update Invoice\" : \"Create Invoice\"}\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    </Form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AAOA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAKA;AACA;AACA;AAzCA;;;;;;;;;;;;;;;;;;;;;;;AA2CA,yBAAyB;AACzB,MAAM,oBAAoB,CAAA,GAAA,oIAAA,CAAA,SACjB,AAAD,EAAE;IACN,MAAM,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAY;KAAW;IACrC,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,cAAc,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;IACnB,UAAU,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;IACf,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC/B,OAAO,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC1B,OAAO,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC1B,gBAAgB,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,OAAO,CAAC;IACpC,OAAO,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EACX,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;QACP,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAC/B,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,MAAM;QAC/B,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAC9B,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAC/B,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;QACxC,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;QAC1C,eAAe,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,OAAO,CAAC;IACrC,IACA,GAAG,CAAC,GAAG;IACT,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC;IACzB,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC;IAC3B,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC;AAC/B,GACC,MAAM,CACL,CAAC;IACC,oDAAoD;IACpD,IAAI,KAAK,IAAI,KAAK,YAAY;QAC5B,OAAO,CAAC,CAAC,KAAK,WAAW;IAC3B;IACA,oDAAoD;IACpD,IAAI,KAAK,IAAI,KAAK,YAAY;QAC5B,OAAO,CAAC,CAAC,KAAK,WAAW;IAC3B;IACA,OAAO;AACT,GACA;IACE,SAAS;IACT,MAAM;QAAC;QAAe;KAAc;AACtC;AAWG,SAAS,YAAY,EAC1B,OAAO,EACP,QAAQ,EACR,YAAY,EACK;IACjB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IAC3C,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IAC3C,MAAM,YAAY,eAAe,QAAQ,EAAE;IAC3C,MAAM,YAAY,eAAe,QAAQ,EAAE;IAE3C,+DAA+D;IAC/D,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAqB;QACtC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe,UACX;YACE,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW,IAAI;YACpC,aAAa,QAAQ,WAAW,IAAI;YACpC,cAAc,IAAI,KAAK,QAAQ,YAAY;YAC3C,UAAU,IAAI,KAAK,QAAQ,QAAQ;YACnC,YAAY,QAAQ,UAAU,IAAI;YAClC,OAAO,QAAQ,KAAK,IAAI;YACxB,OAAO,QAAQ,KAAK,IAAI;YACxB,gBAAgB,QAAQ,cAAc,IAAI;YAC1C,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,OAAS,CAAC;oBACnC,aAAa,KAAK,WAAW,IAAI;oBACjC,UAAU,OAAO,KAAK,QAAQ,KAAK;oBACnC,YAAY,OAAO,KAAK,UAAU,KAAK;oBACvC,aAAa,OAAO,KAAK,WAAW,KAAK;oBACzC,UAAU,KAAK,QAAQ,KAAK,YAAY,OAAO,KAAK,QAAQ,KAAK,IAAI;oBACrE,YAAY,KAAK,UAAU,KAAK,YAAY,OAAO,KAAK,UAAU,KAAK,IAAI;oBAC3E,eAAe,KAAK,aAAa,KAAK;gBACxC,CAAC,MAAM,EAAE;YACT,UAAU,OAAO,QAAQ,QAAQ,KAAK;YACtC,YAAY,OAAO,QAAQ,UAAU,KAAK;YAC1C,cAAc,OAAO,QAAQ,YAAY,KAAK;QAChD,IACA;YACE,MAAM;YACN,aAAa;YACb,aAAa;YACb,cAAc,IAAI;YAClB,UAAU,IAAI;YACd,YAAY;YACZ,OAAO;YACP,OAAO;YACP,gBAAgB;YAChB,OAAO;gBACL;oBACE,aAAa;oBACb,UAAU;oBACV,YAAY;oBACZ,aAAa;oBACb,UAAU;oBACV,YAAY;oBACZ,eAAe;gBACjB;aACD;YACD,UAAU;YACV,YAAY;YACZ,cAAc;QAChB;IACN;IAEA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C,SAAS,KAAK,OAAO;QACrB,MAAM;IACR;IAEA,qCAAqC;IACrC,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,MAAM,YAAY,KAAK,KAAK,CAAC;IAE7B,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,wDAAwD;YACxD,MAAM,cAAc,WAAW,MAAM,CACnC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,WAAW,IAAI,CAAC,GAC3C;YAGF,2DAA2D;YAC3D,MAAM,YAAY,WAAW,MAAM,CACjC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,UAAU,IAAI,CAAC,GAC1C;YAGF,6CAA6C;YAC7C,MAAM,gBAAgB,cAAc;YAEpC,qBAAqB;YACrB,KAAK,QAAQ,CAAC,YAAY,gBAAgB,oCAAoC;YAC9E,KAAK,QAAQ,CAAC,cAAc;YAC5B,KAAK,QAAQ,CAAC,gBAAgB;QAChC;IACF,GAAG;QAAC;QAAY;KAAK;IAErB,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,0CAA0C;QAC1C,MAAM,WAAW;YACf,MAAM,OAAO,IAAI;YACjB,cAAc,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,YAAY,EAAE;YAC1C,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,QAAQ,EAAE;YAClC,YAAY,OAAO,UAAU;YAC7B,OAAO,OAAO,KAAK;YACnB,OAAO,OAAO,KAAK;YACnB,gBAAgB,OAAO,cAAc;YACrC,UAAU,OAAO,QAAQ;YACzB,YAAY,OAAO,UAAU;YAC7B,cAAc,OAAO,YAAY;YACjC,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;oBACjC,GAAG,IAAI;oBACP,UAAU,KAAK,QAAQ,IAAI;oBAC3B,YAAY,KAAK,UAAU,IAAI;gBACjC,CAAC;QACH;QAEA,qDAAqD;QACrD,MAAM,gBAAgB;YACpB,GAAG,QAAQ;YACX,GAAI,OAAO,IAAI,KAAK,cAAc,OAAO,WAAW,GAAG;gBAAE,aAAa,OAAO,WAAW;YAAC,IAAI,CAAC,CAAC;YAC/F,GAAI,OAAO,IAAI,KAAK,cAAc,OAAO,WAAW,GAAG;gBAAE,aAAa,OAAO,WAAW;YAAC,IAAI,CAAC,CAAC;QACjG;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,SAAS;IACX;IAEA,kBAAkB;IAClB,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD;IAE1C,2DAA2D;IAC3D,MAAM,qBAAqB,CAAC;QAC1B,MAAM,OAAO,KAAK,SAAS,CAAC,CAAC,MAAM,EAAE,OAAO;QAE5C,4BAA4B;QAC5B,MAAM,WAAW,OAAO,KAAK,QAAQ,KAAK,WACtC,WAAW,KAAK,QAAQ,KAAK,IAC7B,KAAK,QAAQ,IAAI;QAErB,MAAM,YAAY,OAAO,KAAK,UAAU,KAAK,WACzC,WAAW,KAAK,UAAU,KAAK,IAC/B,KAAK,UAAU,IAAI;QAEvB,qDAAqD;QACrD,MAAM,aAAa,WAAW;QAE9B,uCAAuC;QACvC,IAAI,YAAY;QAChB,IAAI,cAAc;QAClB,MAAM,gBAAgB,KAAK,SAAS,CAAC;QAErC,IAAI,eAAe;YACjB,yDAAyD;YACzD,MAAM,UAAU,OAAO,KAAK,QAAQ,KAAK,WACrC,WAAW,KAAK,QAAQ,KAAK,IAC7B,OAAO,KAAK,QAAQ,KAAK,WACzB,KAAK,QAAQ,GACb;YAEJ,mCAAmC;YACnC,cAAc,YAAY;YAE1B,IAAI,CAAC,aAAa;gBAChB,YAAY,aAAa,CAAC,UAAU,GAAG;YACzC;YAEA,0EAA0E;YAC1E,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC,EAAE;QAC3C,OAAO;YACL,sDAAsD;YACtD,cAAc;YACd,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC,EAAE;QAC3C;QAEA,gDAAgD;QAChD,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC,EAAE,WAAW,WAAW,OAAO,CAAC;QAC1E,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,WAAW,CAAC,EAAE,WAAW,UAAU,OAAO,CAAC;QACxE,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,cAAc,CAAC,EAAE;IAChD;IAIA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YAAK,UAAU,KAAK,YAAY,CAAC;YAAe,WAAU;;8BACzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;gDAA0B;8DAE7C,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjC,8OAAC,kIAAA,CAAA,SAAM;4CACL,eAAe,MAAM,QAAQ;4CAC7B,cAAc,MAAM,KAAK;;8DAEzB,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;;;;;;8DAG7B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAGjC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;sDAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;wBAMjB,cAAc,2BACb,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;gDAA0B;8DAE7C,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjC,8OAAC,kIAAA,CAAA,SAAM;4CACL,eAAe,CAAC,QAAU,MAAM,QAAQ,CAAC,SAAS;4CAClD,OAAO,MAAM,KAAK,EAAE,cAAc;;8DAElC,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;;;;;;8DAG7B,8OAAC,kIAAA,CAAA,gBAAa;8DACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;4DAET,OAAO,SAAS,EAAE,CAAC,QAAQ;sEAE1B,SAAS,IAAI;2DAHT,SAAS,EAAE;;;;;;;;;;;;;;;;sDAQxB,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;sDAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;iDAKlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;gDAA0B;8DAE7C,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjC,8OAAC,kIAAA,CAAA,SAAM;4CACL,eAAe,CAAC,QAAU,MAAM,QAAQ,CAAC,SAAS;4CAClD,OAAO,MAAM,KAAK,EAAE,cAAc;;8DAElC,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;;;;;;8DAG7B,8OAAC,kIAAA,CAAA,gBAAa;8DACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;4DAET,OAAO,SAAS,EAAE,CAAC,QAAQ;sEAE1B,SAAS,IAAI;2DAHT,SAAS,EAAE;;;;;;;;;;;;;;;;sDAQxB,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;sDAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAOpB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,CAAC,MAAM,KAAK,IAAI;;gEAGjB,MAAM,KAAK,GACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,uBAEpB,8OAAC;8EAAK;;;;;;8EAER,8OAAC,8MAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8DAI9B,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;oDAAa,OAAM;8DAC3C,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wDACP,MAAK;wDACL,UAAU,MAAM,KAAK;wDACrB,UAAU,MAAM,QAAQ;wDACxB,YAAY;;;;;;;;;;;;;;;;;sDAIlB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAMlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,CAAC,MAAM,KAAK,IAAI;;gEAGjB,MAAM,KAAK,GACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,uBAEpB,8OAAC;8EAAK;;;;;;8EAER,8OAAC,8MAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8DAI9B,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;oDAAa,OAAM;8DAC3C,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wDACP,MAAK;wDACL,UAAU,MAAM,KAAK;wDACrB,UAAU,MAAM,QAAQ;wDACxB,YAAY;;;;;;;;;;;;;;;;;sDAIlB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAMlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,aAAY;gDAAa,GAAG,KAAK;;;;;;;;;;;sDAE1C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAMlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,aAAY;gDAAU,GAAG,KAAK;;;;;;;;;;;sDAEvC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAMlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAY;;;;;;8DAGjC,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,MAAM,KAAK;gDACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS3C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IACP,OAAO;4CACL,aAAa;4CACb,UAAU;4CACV,YAAY;4CACZ,aAAa;4CACb,UAAU;4CACV,YAAY;4CACZ,eAAe;wCACjB;;sDAGF,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAIpC,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAEb,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAM,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC;gDAClC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAoB,GAAG,KAAK;;;;;;;;;;;0EAEjD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;sDAOpB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAM,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC;oDAC/B,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,MAAK;wEACL,WAAU;wEACT,GAAG,KAAK;wEACT,UAAU,CAAC;4EACT,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EAC7C,mBAAmB;wEACrB;;;;;;;;;;;8EAGJ,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8DAMlB,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAM,CAAC,MAAM,EAAE,MAAM,WAAW,CAAC;oDACjC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,MAAK;wEACL,WAAU;wEACT,GAAG,KAAK;wEACT,UAAU,CAAC;4EACT,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EAC7C,mBAAmB;wEACrB;;;;;;;;;;;8EAGJ,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;gDAMjB,KAAK,KAAK,CAAC,mCACV,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAM,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC;oDAC/B,QAAQ,CAAC,EAAE,KAAK,EAAE;wDAChB,MAAM,cAAc,KAAK,SAAS,CAAC,CAAC,MAAM,EAAE,MAAM,cAAc,CAAC;wDAEjE,qBACE,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,MAAK;wEACL,WAAU;wEACT,GAAG,KAAK;wEACT,UAAU,CAAC;4EACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,WAAW,EAAE,MAAM,CAAC,KAAK,IAAI;4EACnE,MAAM,QAAQ,CAAC;4EACf,mBAAmB;wEACrB;wEACA,OAAO,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK,YAAY,MAAM,KAAK,GAAG;wEACzE,aAAY;;;;;;;;;;;8EAGhB,8OAAC,gIAAA,CAAA,kBAAe;8EACb,cAAc,oBAAoB;;;;;;8EAErC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;oDAGlB;;;;;;8DAMJ,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAM,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC;oDAClC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,MAAK;wEACJ,GAAG,KAAK;wEACT,QAAQ;wEACR,WAAU;;;;;;;;;;;8EAGd,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;8EAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;wCAOnB,OAAO,MAAM,GAAG,mBACf,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO;4CACtB,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;+BA5IhC,MAAM,EAAE;;;;;;;;;;;8BAqJvB,8OAAC,gIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8CACP,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wCACP,aAAY;wCACX,GAAG,KAAK;wCACT,MAAM;;;;;;;;;;;8CAGV,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAMlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACJ,GAAG,KAAK;gDACT,QAAQ;;;;;;;;;;;sDAGZ,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;sDAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAKlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACJ,GAAG,KAAK;gDACT,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;sDAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAKlB,8OAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACJ,GAAG,KAAK;gDACT,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;sDAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8BAOpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,IAAI;4BAC1B,UAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU;sCAC7B,eAAe,cAAc,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;AAMvE", "debugId": null}}, {"offset": {"line": 7233, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/invoices/api/invoice-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  CreateInvoicePaymentRequest,\r\n  CreateInvoiceRequest,\r\n  Invoice,\r\n  InvoiceFilters,\r\n  PaginatedInvoiceResponse,\r\n  UpdateInvoiceRequest,\r\n} from \"../types\";\r\n\r\n/**\r\n * Invoice Service\r\n * Handles API calls for invoice operations\r\n */\r\nconst invoiceService = {\r\n  /**\r\n   * Get all invoices with optional filters, pagination, and sorting\r\n   */\r\n  getInvoices: async (\r\n    filters?: InvoiceFilters\r\n  ): Promise<PaginatedInvoiceResponse> => {\r\n    try {\r\n      console.log(\"Fetching invoices with filters:\", filters);\r\n\r\n      // Prepare request parameters\r\n      const params = {\r\n        ...filters,\r\n        // Convert date objects to ISO strings if they exist\r\n        start_date: filters?.start_date ? new Date(filters.start_date).toISOString().split('T')[0] : undefined,\r\n        end_date: filters?.end_date ? new Date(filters.end_date).toISOString().split('T')[0] : undefined,\r\n      };\r\n\r\n      // Try to fetch from the API with retry logic\r\n      let retryCount = 0;\r\n      const maxRetries = 2;\r\n      let response;\r\n\r\n      while (retryCount <= maxRetries) {\r\n        try {\r\n          response = await apiClient.get<PaginatedInvoiceResponse>(\"/invoices\", {\r\n            params,\r\n          });\r\n          break; // Success, exit the retry loop\r\n        } catch (retryError: any) {\r\n          retryCount++;\r\n\r\n          // If we've reached max retries or it's not a network error, throw\r\n          if (retryCount > maxRetries || (retryError.response && retryError.response.status !== 0)) {\r\n            throw retryError;\r\n          }\r\n\r\n          // Wait before retrying (exponential backoff)\r\n          const delay = Math.min(1000 * 2 ** retryCount, 5000);\r\n          console.warn(`Retrying invoice fetch (${retryCount}/${maxRetries}) after ${delay}ms`);\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n        }\r\n      }\r\n\r\n      console.log(\"Invoice API response:\", response);\r\n\r\n      // If the response doesn't have the expected structure, return a default structure\r\n      if (!response || !response.data) {\r\n        console.warn(\"API returned unexpected response structure:\", response);\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            page: filters?.page || 1,\r\n            totalPages: 0,\r\n            totalItems: 0,\r\n            limit: filters?.limit || 10\r\n          }\r\n        };\r\n      }\r\n\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching invoices:\", error);\r\n\r\n      // Provide more detailed error information\r\n      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';\r\n      console.error(`Invoice fetch error details: ${errorMessage}`);\r\n\r\n      // Return a default structure instead of throwing\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          page: filters?.page || 1,\r\n          totalPages: 0,\r\n          totalItems: 0,\r\n          limit: filters?.limit || 10\r\n        },\r\n        error: errorMessage // Add error information to the response\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get an invoice by ID\r\n   */\r\n  getInvoiceById: async (id: number): Promise<Invoice> => {\r\n    try {\r\n      // Try to fetch with retry logic\r\n      let retryCount = 0;\r\n      const maxRetries = 2;\r\n      let response;\r\n\r\n      while (retryCount <= maxRetries) {\r\n        try {\r\n          response = await apiClient.get<{ data: Invoice, success: boolean }>(`/invoices/${id}`);\r\n          break; // Success, exit the retry loop\r\n        } catch (retryError: any) {\r\n          retryCount++;\r\n\r\n          // If we've reached max retries or it's not a network error, throw\r\n          if (retryCount > maxRetries || (retryError.response && retryError.response.status !== 0)) {\r\n            throw retryError;\r\n          }\r\n\r\n          // Wait before retrying (exponential backoff)\r\n          const delay = Math.min(1000 * 2 ** retryCount, 5000);\r\n          console.warn(`Retrying invoice fetch (${retryCount}/${maxRetries}) after ${delay}ms`);\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n        }\r\n      }\r\n\r\n      if (!response || !response.data) {\r\n        throw new Error(`Invoice with ID ${id} not found or response format invalid`);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error(`Error fetching invoice with ID ${id}:`, error);\r\n\r\n      // Provide more detailed error information\r\n      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';\r\n      console.error(`Invoice fetch error details: ${errorMessage}`);\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new invoice\r\n   */\r\n  createInvoice: async (data: CreateInvoiceRequest): Promise<Invoice> => {\r\n    try {\r\n      const response = await apiClient.post<{ data: Invoice }>(\"/invoices\", data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating invoice:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update an existing invoice\r\n   */\r\n  updateInvoice: async (\r\n    id: number,\r\n    data: UpdateInvoiceRequest\r\n  ): Promise<Invoice> => {\r\n    try {\r\n      const response = await apiClient.put<{ data: Invoice }>(\r\n        `/invoices/${id}`,\r\n        data\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error updating invoice with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete an invoice\r\n   */\r\n  deleteInvoice: async (id: number): Promise<void> => {\r\n    try {\r\n      await apiClient.delete(`/invoices/${id}`);\r\n    } catch (error) {\r\n      console.error(`Error deleting invoice with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Add a payment to an invoice\r\n   */\r\n  addInvoicePayment: async (\r\n    invoiceId: number,\r\n    data: CreateInvoicePaymentRequest\r\n  ): Promise<any> => {\r\n    try {\r\n      const response = await apiClient.post(\r\n        `/invoices/${invoiceId}/payments`,\r\n        data\r\n      );\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error adding payment to invoice with ID ${invoiceId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get invoice payments\r\n   */\r\n  getInvoicePayments: async (invoiceId: number): Promise<any> => {\r\n    try {\r\n      const response = await apiClient.get(`/invoices/${invoiceId}/payments`);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\r\n        `Error fetching payments for invoice with ID ${invoiceId}:`,\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Generate invoice PDF\r\n   */\r\n  generateInvoicePdf: async (invoiceId: number): Promise<Blob> => {\r\n    try {\r\n      const response = await apiClient.get(`/invoices/${invoiceId}/pdf`, {\r\n        responseType: 'blob'\r\n      });\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\r\n        `Error generating PDF for invoice with ID ${invoiceId}:`,\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\nexport default invoiceService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAUA;;;CAGC,GACD,MAAM,iBAAiB;IACrB;;GAEC,GACD,aAAa,OACX;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,6BAA6B;YAC7B,MAAM,SAAS;gBACb,GAAG,OAAO;gBACV,oDAAoD;gBACpD,YAAY,SAAS,aAAa,IAAI,KAAK,QAAQ,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBAC7F,UAAU,SAAS,WAAW,IAAI,KAAK,QAAQ,QAAQ,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YACzF;YAEA,6CAA6C;YAC7C,IAAI,aAAa;YACjB,MAAM,aAAa;YACnB,IAAI;YAEJ,MAAO,cAAc,WAAY;gBAC/B,IAAI;oBACF,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAA2B,aAAa;wBACpE;oBACF;oBACA,OAAO,+BAA+B;gBACxC,EAAE,OAAO,YAAiB;oBACxB;oBAEA,kEAAkE;oBAClE,IAAI,aAAa,cAAe,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,KAAK,GAAI;wBACxF,MAAM;oBACR;oBAEA,6CAA6C;oBAC7C,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,YAAY;oBAC/C,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,MAAM,EAAE,CAAC;oBACpF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;YAEA,QAAQ,GAAG,CAAC,yBAAyB;YAErC,kFAAkF;YAClF,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,EAAE;gBAC/B,QAAQ,IAAI,CAAC,+CAA+C;gBAC5D,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,MAAM,SAAS,QAAQ;wBACvB,YAAY;wBACZ,YAAY;wBACZ,OAAO,SAAS,SAAS;oBAC3B;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,0CAA0C;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;YACvE,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,cAAc;YAE5D,iDAAiD;YACjD,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,MAAM,SAAS,QAAQ;oBACvB,YAAY;oBACZ,YAAY;oBACZ,OAAO,SAAS,SAAS;gBAC3B;gBACA,OAAO,aAAa,wCAAwC;YAC9D;QACF;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,gCAAgC;YAChC,IAAI,aAAa;YACjB,MAAM,aAAa;YACnB,IAAI;YAEJ,MAAO,cAAc,WAAY;gBAC/B,IAAI;oBACF,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAsC,CAAC,UAAU,EAAE,IAAI;oBACrF,OAAO,+BAA+B;gBACxC,EAAE,OAAO,YAAiB;oBACxB;oBAEA,kEAAkE;oBAClE,IAAI,aAAa,cAAe,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,KAAK,GAAI;wBACxF,MAAM;oBACR;oBAEA,6CAA6C;oBAC7C,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,YAAY;oBAC/C,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,MAAM,EAAE,CAAC;oBACpF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;YAEA,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,EAAE;gBAC/B,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,GAAG,qCAAqC,CAAC;YAC9E;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YAEvD,0CAA0C;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;YACvE,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,cAAc;YAE5D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAoB,aAAa;YACtE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,eAAe,OACb,IACA;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,UAAU,EAAE,IAAI,EACjB;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,mBAAmB,OACjB,WACA;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,UAAU,EAAE,UAAU,SAAS,CAAC,EACjC;YAEF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAC,EAAE;YACvE,MAAM;QACR;IACF;IAEA;;GAEC,GACD,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,SAAS,CAAC;YACtE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CACX,CAAC,4CAA4C,EAAE,UAAU,CAAC,CAAC,EAC3D;YAEF,MAAM;QACR;IACF;IAEA;;GAEC,GACD,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,EAAE;gBACjE,cAAc;YAChB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CACX,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC,EACxD;YAEF,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 7420, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/invoices/hooks/use-invoices.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport invoiceService from \"../api/invoice-service\";\r\nimport {\r\n  CreateInvoicePaymentRequest,\r\n  CreateInvoiceRequest,\r\n  InvoiceFilters,\r\n  UpdateInvoiceRequest,\r\n} from \"../types\";\r\nimport { toast } from \"sonner\";\r\n\r\n/**\r\n * Hook to fetch all invoices with optional filters\r\n */\r\nexport const useInvoices = (filters?: InvoiceFilters) => {\r\n  return useQuery({\r\n    queryKey: [\"invoices\", filters],\r\n    queryFn: () => invoiceService.getInvoices(filters),\r\n    placeholderData: \"keep\" as any,\r\n    retry: 2, // Increase retry attempts\r\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff\r\n    refetchOnWindowFocus: false,\r\n    onError: (error) => {\r\n      console.error(\"Error in useInvoices hook:\", error);\r\n      toast.error(\"Failed to load invoices. Please try again later.\");\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch a single invoice by ID\r\n */\r\nexport const useInvoice = (id: number) => {\r\n  return useQuery({\r\n    queryKey: [\"invoice\", id],\r\n    queryFn: () => invoiceService.getInvoiceById(id),\r\n    enabled: !!id,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to create a new invoice\r\n */\r\nexport const useCreateInvoice = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateInvoiceRequest) => invoiceService.createInvoice(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"invoices\"] });\r\n      toast.success(\"Invoice created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to create invoice\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update an existing invoice\r\n */\r\nexport const useUpdateInvoice = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UpdateInvoiceRequest) =>\r\n      invoiceService.updateInvoice(id, data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"invoices\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"invoice\", id] });\r\n      toast.success(\"Invoice updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to update invoice\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete an invoice\r\n */\r\nexport const useDeleteInvoice = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => invoiceService.deleteInvoice(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"invoices\"] });\r\n      toast.success(\"Invoice deleted successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to delete invoice\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to add a payment to an invoice\r\n */\r\nexport const useAddInvoicePayment = (invoiceId: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateInvoicePaymentRequest) =>\r\n      invoiceService.addInvoicePayment(invoiceId, data),\r\n    onSuccess: () => {\r\n      // Force refetch the invoice data to get updated payment information\r\n      queryClient.invalidateQueries({ queryKey: [\"invoice\", invoiceId] });\r\n      queryClient.invalidateQueries({ queryKey: [\"invoices\"] });\r\n\r\n      // Also invalidate any payment-specific queries\r\n      queryClient.invalidateQueries({ queryKey: [\"invoice-payments\", invoiceId] });\r\n\r\n      toast.success(\"Payment added successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to add payment\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch invoice payments\r\n */\r\nexport const useInvoicePayments = (invoiceId: number) => {\r\n  return useQuery({\r\n    queryKey: [\"invoice-payments\", invoiceId],\r\n    queryFn: () => invoiceService.getInvoicePayments(invoiceId),\r\n    enabled: !!invoiceId,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to generate invoice PDF\r\n */\r\nexport const useGenerateInvoicePdf = () => {\r\n  return useMutation({\r\n    mutationFn: (invoiceId: number) => invoiceService.generateInvoicePdf(invoiceId),\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to generate PDF\"\r\n      );\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAOA;AAVA;;;;AAeO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAQ;QAC/B,SAAS,IAAM,wJAAA,CAAA,UAAc,CAAC,WAAW,CAAC;QAC1C,iBAAiB;QACjB,OAAO;QACP,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QACjE,sBAAsB;QACtB,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAKO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAG;QACzB,SAAS,IAAM,wJAAA,CAAA,UAAc,CAAC,cAAc,CAAC;QAC7C,SAAS,CAAC,CAAC;IACb;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA+B,wJAAA,CAAA,UAAc,CAAC,aAAa,CAAC;QACzE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErC;IACF;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OACX,wJAAA,CAAA,UAAc,CAAC,aAAa,CAAC,IAAI;QACnC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAG;YAAC;YAC1D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErC;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,wJAAA,CAAA,UAAc,CAAC,aAAa,CAAC;QACzD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErC;IACF;AACF;AAKO,MAAM,uBAAuB,CAAC;IACnC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OACX,wJAAA,CAAA,UAAc,CAAC,iBAAiB,CAAC,WAAW;QAC9C,WAAW;YACT,oEAAoE;YACpE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAU;YAAC;YACjE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YAEvD,+CAA+C;YAC/C,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAoB;iBAAU;YAAC;YAE1E,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErC;IACF;AACF;AAKO,MAAM,qBAAqB,CAAC;IACjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAoB;SAAU;QACzC,SAAS,IAAM,wJAAA,CAAA,UAAc,CAAC,kBAAkB,CAAC;QACjD,SAAS,CAAC,CAAC;IACb;AACF;AAKO,MAAM,wBAAwB;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,YAAsB,wJAAA,CAAA,UAAc,CAAC,kBAAkB,CAAC;QACrE,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErC;IACF;AACF", "debugId": null}}, {"offset": {"line": 7578, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 7643, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/invoices/new/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { InvoiceForm } from \"@/features/invoices/components/invoice-form\";\r\nimport { useCreateInvoice } from \"@/features/invoices/hooks/use-invoices\";\r\nimport { CreateInvoiceRequest } from \"@/features/invoices/types\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { ArrowLeft, AlertCircle } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { toast } from \"sonner\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\n\r\nexport default function CreateInvoicePage() {\r\n  const router = useRouter();\r\n  const createInvoice = useCreateInvoice();\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const handleCreateInvoice = async (data: CreateInvoiceRequest) => {\r\n    setError(null);\r\n    try {\r\n      await createInvoice.mutateAsync(data);\r\n      toast.success(\"Invoice created successfully\");\r\n      router.push(\"/invoices\");\r\n    } catch (error: any) {\r\n      console.error(\"Error creating invoice:\", error);\r\n\r\n      // Extract error message from the response\r\n      const errorMessage = error.response?.data?.message ||\r\n                          (error.response?.data?.errors && error.response.data.errors.length > 0\r\n                            ? error.response.data.errors.map((e: any) => e.msg).join(\", \")\r\n                            : \"Failed to create invoice\");\r\n\r\n      setError(errorMessage);\r\n      toast.error(errorMessage);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"container mx-auto py-6 space-y-6\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button variant=\"outline\" size=\"icon\" onClick={() => router.back()}>\r\n            <ArrowLeft className=\"h-4 w-4\" />\r\n          </Button>\r\n          <h1 className=\"text-3xl font-bold\">Create New Invoice</h1>\r\n        </div>\r\n\r\n        {error && (\r\n          <Alert variant=\"destructive\">\r\n            <AlertCircle className=\"h-4 w-4\" />\r\n            <AlertTitle>Error</AlertTitle>\r\n            <AlertDescription>{error}</AlertDescription>\r\n          </Alert>\r\n        )}\r\n\r\n        <InvoiceForm\r\n          onSubmit={handleCreateInvoice}\r\n          isSubmitting={createInvoice.isPending}\r\n        />\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,sBAAsB,OAAO;QACjC,SAAS;QACT,IAAI;YACF,MAAM,cAAc,WAAW,CAAC;YAChC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,0CAA0C;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IACjE,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAW,EAAE,GAAG,EAAE,IAAI,CAAC,QACvD,0BAA0B;YAElD,SAAS;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC,+IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,SAAS,IAAM,OAAO,IAAI;sCAC9D,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;;;;;;;gBAGpC,uBACC,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,aAAU;sCAAC;;;;;;sCACZ,8OAAC,iIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;8BAIvB,8OAAC,6JAAA,CAAA,cAAW;oBACV,UAAU;oBACV,cAAc,cAAc,SAAS;;;;;;;;;;;;;;;;;AAK/C", "debugId": null}}]}