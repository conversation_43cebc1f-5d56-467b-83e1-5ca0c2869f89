/**
 * Utility class for invoice calculations
 */
const VatCalculator = require('./vat-calculator');

class InvoiceCalculator {
  /**
   * Calculate invoice item totals
   * @param {Object} item - Invoice item with quantity, unit_price, vat_rate, is_vat_exempt
   * @param {boolean} kraIntegrated - Whether KRA integration is enabled for this invoice
   * @returns {Object} Calculated values including total_price, vat_amount, and vatable_amount
   */
  static calculateItemTotals(item, kraIntegrated = false) {
    const quantity = parseFloat(item.quantity) || 0;
    const unitPrice = parseFloat(item.unit_price) || 0;

    // Calculate total price from quantity and unit price
    const totalPrice = quantity * unitPrice;

    let vatAmount = 0;
    let isVatExempt = false;

    // Only calculate VAT if KRA integration is enabled
    if (kraIntegrated) {
      // Use actual VAT rate from frontend if provided, otherwise use exemption status
      if (item.vat_rate !== undefined && item.vat_rate !== null) {
        const vatRate = parseFloat(item.vat_rate) || 0;
        vatAmount = totalPrice * (vatRate / 100);
        isVatExempt = vatRate === 0;
      } else {
        // Fallback to exemption status
        isVatExempt = item.is_vat_exempt === true;
        const vatRate = isVatExempt ? 0 : 16;
        vatAmount = isVatExempt ? 0 : totalPrice * (vatRate / 100);
      }
    } else {
      // For non-KRA invoices, no VAT calculation
      vatAmount = 0;
      isVatExempt = true;
    }

    // Calculate vatable amount (total minus VAT)
    const vatableAmount = totalPrice - vatAmount;

    return {
      total_price: parseFloat(totalPrice.toFixed(2)),
      vat_amount: parseFloat(vatAmount.toFixed(2)),
      vatable_amount: parseFloat(vatableAmount.toFixed(2)),
      is_vat_exempt: isVatExempt
    };
  }

  /**
   * Calculate invoice totals from items
   * @param {Array} items - Array of invoice items
   * @returns {Object} Calculated invoice totals
   */
  static calculateInvoiceTotals(items) {
    let totalAmount = 0;
    let totalVatAmount = 0;

    items.forEach(item => {
      totalAmount += parseFloat(item.total_price) || 0;
      totalVatAmount += parseFloat(item.vat_amount) || 0;
    });

    // Calculate vatable amount (total minus VAT)
    const vatableAmount = totalAmount - totalVatAmount;

    return {
      total_amount: parseFloat(totalAmount.toFixed(2)),
      vat_amount: parseFloat(totalVatAmount.toFixed(2)),
      subtotal: parseFloat(vatableAmount.toFixed(2)) // This is the vatable amount
    };
  }

  /**
   * Process invoice items for creation or update
   * @param {Array} items - Array of invoice items from request
   * @param {string} invoiceType - Type of invoice ('supplier' or 'customer')
   * @param {boolean} kraIntegrated - Whether KRA integration is enabled for this invoice
   * @returns {Array} Processed invoice items with calculated values
   */
  static processInvoiceItems(items, invoiceType, kraIntegrated = false) {
    if (!items || !Array.isArray(items)) {
      return [];
    }

    return items.map(item => {
      // Calculate using the updated method that respects KRA integration and actual VAT rates
      const calculatedValues = this.calculateItemTotals(item, kraIntegrated);

      return {
        ...item,
        ...calculatedValues
      };
    });
  }
}

module.exports = InvoiceCalculator;
